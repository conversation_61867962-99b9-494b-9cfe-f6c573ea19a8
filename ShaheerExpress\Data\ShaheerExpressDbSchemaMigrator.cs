﻿using Volo.Abp.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace ShaheerExpress.Data;

public class ShaheerExpressDbSchemaMigrator : ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public ShaheerExpressDbSchemaMigrator(
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        
        /* We intentionally resolving the ShaheerExpressDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<ShaheerExpressDbContext>()
            .Database
            .MigrateAsync();

    }
}
