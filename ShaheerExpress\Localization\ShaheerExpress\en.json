{"culture": "en", "texts": {"AppName": "ShaheerExpress", "Welcome_Title": "Welcome", "Welcome_Text": "This is a minimalist, single layer application startup template for the ABP Framework.", "Permission:Dashboard": "Dashboard", "Menu:Dashboard": "Dashboard", "Dashboard": "Dashboard", "ExternalProvider:Google": "Google", "ExternalProvider:Google:ClientId": "Client ID", "ExternalProvider:Google:ClientSecret": "Client secret", "ExternalProvider:Microsoft": "Microsoft", "ExternalProvider:Microsoft:ClientId": "Client ID", "ExternalProvider:Microsoft:ClientSecret": "Client secret", "ExternalProvider:Twitter": "Twitter", "ExternalProvider:Twitter:ConsumerKey": "Consumer key", "ExternalProvider:Twitter:ConsumerSecret": "Consumer secret", "Menu:Home": "Home", "Actions": "Actions", "Close": "Close", "Delete": "Delete", "Edit": "Edit", "Name": "Name", "CreationTime": "Creation time", "AreYouSure": "Are you sure?", "SuccessfullyDeleted": "Successfully deleted!", "Permission:Authors": "Authors", "Permission:Create": "Create", "Permission:Edit": "Edit", "Permission:Delete": "Delete", "Authors": "Authors", "NewAuthor": "New Author", "DeleteConfirmationMessage": "Are you sure you want to delete this record?", "Search": "Search", "Pick": "Pick", "SeeAdvancedFilters": "Filters", "ItemAlreadyAdded": "This item is already added.", "ExportToExcel": "Export to Excel", "AllItemsAreSelected": "All {0} items are selected", "OneItemOnThisPageIsSelected": "1 item on this page is selected", "NumberOfItemsOnThisPageAreSelected": "All {0} items on this page are selected", "SelectAllItems": "Select all {0} items", "ClearSelection": "Clear selection", "DeleteAllRecords": "Are you sure you want to delete all records?", "DeleteSelectedRecords": "Are you sure you want to delete {0} record(s)?", "UploadFailedMessage": "Upload Failed: Unsupported file format or file size too large. Please ensure the file meets the required format and size limits, and try again.", "DownloadSelectedFile": "Download selected file", "RemoveSelectedFile": "Remove selected file", "FileUploading": "File uploading...", "MaxFileSizeLimit": "Max file size: {0}mb", "Filters": "Filters", "Birthdate": "Birthdate", "Bio": "Bio", "MinBirthdate": "Min Birthdate", "MaxBirthdate": "Max Birthdate", "Menu:Authors": "Authors", "Menu:Facebook": "Facebook", "Menu:Facebook:Connection": "Connection", "Menu:Facebook:Pages": "Pages", "Menu:Facebook:Posts": "Posts", "Menu:Campaigns": "Campaigns", "Permission:Facebook": "Facebook", "Permission:Facebook.Connect": "Connect Facebook", "Permission:Facebook.Disconnect": "Disconnect Facebook", "Permission:Facebook.ViewPages": "View Facebook Pages", "Permission:Facebook.ManagePages": "Manage Facebook Pages", "Permission:Posts": "Posts", "Permission:Posts.View": "View Posts", "Permission:Posts.Sync": "Sync Posts", "Permission:Campaigns": "Campaigns", "Permission:Campaigns.View": "View Campaigns", "Permission:Campaigns.Create": "Create Campaigns", "Permission:Campaigns.Edit": "Edit Campaigns", "Permission:Campaigns.Delete": "Delete Campaigns", "Permission:Campaigns.Activate": "Activate Campaigns", "Permission:Campaigns.Deactivate": "Deactivate Campaigns", "Permission:Campaigns.ViewActivities": "View Campaign Activities", "Facebook:Connection": "Facebook Connection", "Facebook:ConnectionDescription": "Manage your Facebook account connection and permissions", "Facebook:RefreshStatus": "Refresh Status", "Facebook:RedirectionNotice": "Redirected from another page", "Facebook:RedirectionMessage": "You were redirected from {0} because Facebook connection is required.", "Facebook:AdvancedActions": "Advanced Actions", "Facebook:TokenDetails": "Token Details", "Facebook:WhatYouCanDo": "What You Can Do After Connecting", "Facebook:PageManagement": "Page Management", "Facebook:AutoReplyFeatures": "Auto-Reply Features", "Facebook:ImportPages": "Import Pages", "Facebook:ViewPosts": "View posts from your Pages", "Facebook:MonitorEngagement": "Monitor page engagement metrics", "Facebook:AutoReplyComments": "Automatically reply to comments", "Facebook:CreateCampaigns": "Create and manage auto-reply campaigns", "Facebook:ManageKeywords": "Set up keyword-based responses", "Facebook:ViewAnalytics": "View detailed analytics and reports", "Facebook:NotConnected": "Facebook Not Connected", "Facebook:Connected": "Facebook Connected", "Facebook:ConnectionIssue": "Facebook Connection Issue", "Facebook:ConnectDescription": "Connect your Facebook account to enable auto-reply functionality for your Facebook Pages.", "Facebook:Connect": "Connect Facebook", "Facebook:Reconnect": "Reconnect", "Facebook:Disconnect": "Disconnect", "Facebook:ValidateToken": "Validate Token", "Facebook:RevokePermissions": "Revoke Permissions", "Facebook:LoadingStatus": "Loading connection status...", "Facebook:TokenExpired": "Your Facebook access token has expired and needs to be renewed.", "Facebook:MissingPermissions": "Missing required permissions:", "Facebook:TokenExpiresAt": "Token expires:", "Facebook:TokenStatus": "Token Status", "Facebook:Valid": "<PERSON><PERSON>", "Facebook:Invalid": "Invalid", "Facebook:ExpiresAt": "Expires At", "Facebook:MissingScopes": "Missing <PERSON><PERSON><PERSON>", "Facebook:Error": "Error", "Facebook:ErrorConnecting": "Failed to connect to Facebook", "Facebook:ErrorReconnecting": "Failed to reconnect to Facebook", "Facebook:ErrorDisconnecting": "Failed to disconnect Facebook account", "Facebook:ErrorRevokingPermissions": "Failed to revoke Facebook permissions", "Facebook:ErrorLoadingStatus": "Error loading Facebook connection status", "Facebook:ErrorValidatingToken": "Failed to validate Facebook token", "Facebook:UnableToCheckStatus": "Unable to check connection status", "Facebook:ConnectedSuccessfully": "Facebook account connected successfully", "Facebook:DisconnectedSuccessfully": "Facebook account disconnected successfully", "Facebook:PermissionsRevokedSuccessfully": "Facebook permissions revoked successfully", "Facebook:PermissionsRevokePartial": "Some permissions may not have been revoked", "Facebook:TokenValidationSuccess": "Facebook token is valid", "Facebook:TokenValidationFailed": "Facebook token validation failed", "Facebook:DisconnectConfirmationMessage": "Are you sure you want to disconnect your Facebook account?", "Facebook:RevokePermissionsConfirmationMessage": "Are you sure you want to revoke all Facebook permissions? This will disconnect your account.", "Facebook:RedirectingToFacebook": "Redirecting to Facebook...", "Facebook:ReconnectingToFacebook": "Reconnecting to Facebook...", "Facebook:DisconnectingAccount": "Disconnecting Facebook account...", "Facebook:RevokingPermissions": "Revoking Facebook permissions...", "Facebook:ValidatingToken": "Validating Facebook token...", "Facebook:Pages": "Facebook Pages", "Facebook:PagesDescription": "Manage your Facebook Pages and their settings", "Facebook:PageName": "Page Name", "Facebook:Followers": "Followers", "Facebook:LastSync": "Last Sync", "Facebook:WebhookActive": "Webhook Active", "Facebook:TokenValid": "<PERSON><PERSON>", "Facebook:TokenUnknown": "To<PERSON>", "Facebook:RequiresReconnection": "Requires Reconnection", "Facebook:TokenIssue": "Token Issue", "Facebook:ValidateTokens": "Validate Tokens", "Facebook:SyncAll": "Sync All", "Facebook:Sync": "Sync", "Facebook:EnableWebhook": "Enable Webhook", "Facebook:DisableWebhook": "Disable Webhook", "Facebook:CreateCampaign": "Create Auto-Reply Campaign", "Facebook:TokenIssuesDetected": "Token Issues Detected", "Facebook:TokenIssuesMessage": "{0} of {1} pages have token issues that require attention.", "Facebook:ReconnectAccount": "Reconnect your Facebook account", "Facebook:ReconnectFacebook": "Reconnect Facebook", "Facebook:AllTokensValid": "All {0} page tokens are valid and active.", "Facebook:ConnectionRequired": "Facebook Connection Required", "Facebook:ConnectionRequiredMessage": "You need to connect your Facebook account first to manage your pages.", "Facebook:ConnectNow": "Connect Now", "Facebook:LoadingPages": "Loading Facebook Pages...", "Facebook:NoPagesFound": "No Facebook Pages found. Click \"Import Pages\" to import your Facebook Pages.", "Facebook:ShowingPages": "Showing {0} to {1} of {2} pages", "Facebook:LoadingAvailablePages": "Loading available pages from Facebook...", "Facebook:NoAvailablePages": "No pages available for import. Make sure you have admin access to Facebook pages.", "Facebook:SelectPagesToImport": "Select the pages you want to import:", "Facebook:SelectAll": "Select All", "Facebook:ImportSelected": "Import Selected", "Facebook:ImportingPages": "Importing...", "Facebook:PagesImportedSuccess": "{0} pages imported successfully", "Facebook:SyncAllConfirmation": "Are you sure you want to sync all pages? This may take a few minutes.", "Facebook:SyncAllSuccess": "All pages synced successfully", "Facebook:PageSyncSuccess": "<PERSON> synced successfully", "Facebook:DisconnectPageConfirmation": "Are you sure you want to disconnect this page?", "Facebook:PageDisconnectedSuccess": "<PERSON> disconnected successfully", "Facebook:PageReconnectedSuccess": "<PERSON> reconnected successfully", "Facebook:UnsubscribeWebhookConfirmation": "Are you sure you want to disable webhook for this page?", "Facebook:WebhookSubscribedSuccess": "Webhook subscribed successfully", "Facebook:WebhookUnsubscribedSuccess": "Webhook unsubscribed successfully", "Facebook:SyncingPage": "Syncing page...", "Facebook:DisconnectingPage": "Disconnecting page...", "Facebook:ReconnectingPage": "Reconnecting page...", "Facebook:SubscribingWebhook": "Subscribing to webhook...", "Facebook:UnsubscribingWebhook": "Unsubscribing from webhook...", "Facebook:ErrorLoadingData": "Error loading data", "Facebook:ErrorLoadingPages": "Error loading Facebook pages", "Facebook:ErrorValidatingTokens": "Error validating page tokens", "Facebook:ErrorSyncingPages": "Error syncing pages", "Facebook:ErrorSyncingPage": "Error syncing page", "Facebook:ErrorDisconnectingPage": "Error disconnecting page", "Facebook:ErrorReconnectingPage": "Error reconnecting page", "Facebook:ErrorSubscribingWebhook": "Error subscribing to webhook", "Facebook:ErrorUnsubscribingWebhook": "Error unsubscribing from webhook", "Facebook:ErrorLoadingAvailablePages": "Error loading available pages from Facebook", "Facebook:ErrorImportingPages": "Error importing pages", "Previous": "Previous", "Next": "Next", "Never": "Never"}}