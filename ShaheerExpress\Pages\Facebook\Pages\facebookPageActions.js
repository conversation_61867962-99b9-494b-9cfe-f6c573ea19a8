const FacebookPageActions = {
    props: {
        page: {
            type: Object,
            required: true
        },
        canManage: {
            type: Boolean,
            default: false
        },
        tokenStatus: {
            type: Object,
            default: null
        }
    },
    
    emits: [
        'sync-page',
        'disconnect-page',
        'reconnect-page',
        'subscribe-webhook',
        'unsubscribe-webhook',
        'create-campaign',
        'action-completed'
    ],
    
    data() {
        return {
            isProcessing: false,
            processingMessage: '',
            l: null
        };
    },
    
    computed: {
        hasTokenIssues() {
            return this.tokenStatus && this.tokenStatus.requiresReconnection;
        },
        
        isTokenExpired() {
            return this.tokenStatus && this.tokenStatus.isTokenExpired;
        },
        
        isTokenValid() {
            return this.tokenStatus && this.tokenStatus.isTokenValid && !this.tokenStatus.requiresReconnection;
        }
    },
    
    template: `
        <div class="facebook-page-actions">
            <div v-if="isProcessing" class="d-flex align-items-center">
                <div class="loading-spinner me-2"></div>
                <span class="text-muted">{{ processingMessage }}</span>
            </div>
            
            <div v-else-if="canManage" class="dropdown">
                <button class="btn btn-primary btn-sm dropdown-toggle" 
                        type="button" 
                        :id="'pageActions' + page.id"
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    {{ l('Actions') || 'Actions' }}
                </button>
                <ul class="dropdown-menu page-actions-dropdown" :aria-labelledby="'pageActions' + page.id">
                    <!-- Sync Page -->
                    <li>
                        <a class="dropdown-item" href="#" v-on:click.prevent="syncPage">
                            <i class="fas fa-sync-alt me-2"></i>
                            {{ l('Facebook:Sync') || 'Sync' }}
                        </a>
                    </li>
                    
                    <!-- Webhook Management -->
                    <li v-if="page.isConnected && !page.webhookSubscribed">
                        <a class="dropdown-item" href="#" v-on:click.prevent="subscribeWebhook">
                            <i class="fas fa-bell me-2"></i>
                            {{ l('Facebook:EnableWebhook') || 'Enable Webhook' }}
                        </a>
                    </li>
                    
                    <li v-if="page.webhookSubscribed">
                        <a class="dropdown-item" href="#" v-on:click.prevent="unsubscribeWebhook">
                            <i class="fas fa-bell-slash me-2"></i>
                            {{ l('Facebook:DisableWebhook') || 'Disable Webhook' }}
                        </a>
                    </li>
                    
                    <li><hr class="dropdown-divider"></li>
                    
                    <!-- Connection Management -->
                    <li v-if="page.isConnected">
                        <a class="dropdown-item" href="#" v-on:click.prevent="disconnectPage">
                            <i class="fas fa-times me-2"></i>
                            {{ l('Facebook:Disconnect') || 'Disconnect' }}
                        </a>
                    </li>
                    
                    <li v-else>
                        <a class="dropdown-item" href="#" v-on:click.prevent="reconnectPage">
                            <i class="fas fa-link me-2"></i>
                            {{ l('Facebook:Reconnect') || 'Reconnect' }}
                        </a>
                    </li>
                    
                    <li><hr class="dropdown-divider"></li>
                    
                    <!-- Campaign Creation -->
                    <li>
                        <a class="dropdown-item text-primary" href="#" v-on:click.prevent="createCampaign">
                            <i class="fas fa-cogs me-2"></i>
                            {{ l('Facebook:CreateCampaign') || 'Create Auto-Reply Campaign' }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    `,
    
    methods: {
        async syncPage() {
            try {
                this.isProcessing = true;
                this.processingMessage = this.l('Facebook:SyncingPage') || 'Syncing page...';
                
                await window.shaheerExpress.services.facebookPage.syncPageInfoAsync(this.page.id);
                abp.notify.success(this.l('Facebook:PageSyncSuccess') || 'Page synced successfully');
                
                this.$emit('sync-page', this.page.id);
                this.$emit('action-completed');
            } catch (error) {
                console.error('Error syncing page:', error);
                abp.notify.error(this.l('Facebook:ErrorSyncingPage') || 'Error syncing page');
            } finally {
                this.isProcessing = false;
            }
        },
        
        async disconnectPage() {
            const confirmed = await abp.message.confirm(
                this.l('Facebook:DisconnectPageConfirmation') || 'Are you sure you want to disconnect this page?',
                this.l('AreYouSure') || 'Are you sure?'
            );
            
            if (confirmed) {
                try {
                    this.isProcessing = true;
                    this.processingMessage = this.l('Facebook:DisconnectingPage') || 'Disconnecting page...';
                    
                    await window.shaheerExpress.services.facebookPage.disconnectPageAsync(this.page.id);
                    abp.notify.success(this.l('Facebook:PageDisconnectedSuccess') || 'Page disconnected successfully');
                    
                    this.$emit('disconnect-page', this.page.id);
                    this.$emit('action-completed');
                } catch (error) {
                    console.error('Error disconnecting page:', error);
                    abp.notify.error(this.l('Facebook:ErrorDisconnectingPage') || 'Error disconnecting page');
                } finally {
                    this.isProcessing = false;
                }
            }
        },
        
        async reconnectPage() {
            try {
                this.isProcessing = true;
                this.processingMessage = this.l('Facebook:ReconnectingPage') || 'Reconnecting page...';
                
                await window.shaheerExpress.services.facebookPage.reconnectPageAsync(this.page.id);
                abp.notify.success(this.l('Facebook:PageReconnectedSuccess') || 'Page reconnected successfully');
                
                this.$emit('reconnect-page', this.page.id);
                this.$emit('action-completed');
            } catch (error) {
                console.error('Error reconnecting page:', error);
                abp.notify.error(this.l('Facebook:ErrorReconnectingPage') || 'Error reconnecting page');
            } finally {
                this.isProcessing = false;
            }
        },
        
        async subscribeWebhook() {
            try {
                this.isProcessing = true;
                this.processingMessage = this.l('Facebook:SubscribingWebhook') || 'Subscribing to webhook...';
                
                await window.shaheerExpress.services.facebookPage.subscribeToWebhookAsync(this.page.id);
                abp.notify.success(this.l('Facebook:WebhookSubscribedSuccess') || 'Webhook subscribed successfully');
                
                this.$emit('subscribe-webhook', this.page.id);
                this.$emit('action-completed');
            } catch (error) {
                console.error('Error subscribing to webhook:', error);
                abp.notify.error(this.l('Facebook:ErrorSubscribingWebhook') || 'Error subscribing to webhook');
            } finally {
                this.isProcessing = false;
            }
        },
        
        async unsubscribeWebhook() {
            const confirmed = await abp.message.confirm(
                this.l('Facebook:UnsubscribeWebhookConfirmation') || 'Are you sure you want to disable webhook for this page?',
                this.l('AreYouSure') || 'Are you sure?'
            );
            
            if (confirmed) {
                try {
                    this.isProcessing = true;
                    this.processingMessage = this.l('Facebook:UnsubscribingWebhook') || 'Unsubscribing from webhook...';
                    
                    await window.shaheerExpress.services.facebookPage.unsubscribeFromWebhookAsync(this.page.id);
                    abp.notify.success(this.l('Facebook:WebhookUnsubscribedSuccess') || 'Webhook unsubscribed successfully');
                    
                    this.$emit('unsubscribe-webhook', this.page.id);
                    this.$emit('action-completed');
                } catch (error) {
                    console.error('Error unsubscribing from webhook:', error);
                    abp.notify.error(this.l('Facebook:ErrorUnsubscribingWebhook') || 'Error unsubscribing from webhook');
                } finally {
                    this.isProcessing = false;
                }
            }
        },
        
        createCampaign() {
            this.$emit('create-campaign', this.page.id);
        }
    },
    
    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    }
};
