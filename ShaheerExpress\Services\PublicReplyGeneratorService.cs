using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace ShaheerExpress.Services;

public interface IPublicReplyGeneratorService
{
    string GenerateEmojiReply();
    Task<string> GenerateWelcomeReplyAsync(string firstName);
    string GenerateRandomEmojis(int count = 3);
}

public class PublicReplyGeneratorService : IPublicReplyGeneratorService, ITransientDependency
{
    private readonly ILogger<PublicReplyGeneratorService> _logger;
    private readonly IHostEnvironment _hostEnvironment;
    private readonly Random _random;
    
    // Common emojis for replies
    private readonly string[] _emojis = {
        "😊", "😍", "🥰", "😘", "😎", "🤗", "🙌", "👏", "💪", "🔥",
        "❤️", "💕", "💖", "💯", "✨", "🌟", "⭐", "🎉", "🎊", "🥳",
        "👍", "👌", "🤝", "🙏", "💝", "🎁", "🌈", "☀️", "🌺", "🌸",
        "💐", "🎈", "🎀", "💎", "🏆", "🥇", "🎯", "🚀", "💫", "⚡"
    };

    private List<string>? _welcomeMessages;

    public PublicReplyGeneratorService(
        ILogger<PublicReplyGeneratorService> logger,
        IHostEnvironment hostEnvironment)
    {
        _logger = logger;
        _hostEnvironment = hostEnvironment;
        _random = new Random();
    }

    public string GenerateEmojiReply()
    {
        return GenerateRandomEmojis(3);
    }

    public async Task<string> GenerateWelcomeReplyAsync(string firstName)
    {
        try
        {
            await EnsureWelcomeMessagesLoadedAsync();
            
            var welcomeTag = GetRandomWelcomeMessage();
            var emojiTag = GenerateRandomEmojis(3);
            
            return $"{welcomeTag} {firstName} {emojiTag}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate welcome reply, using fallback");
            return $"Welcome {firstName} 😊🎉✨";
        }
    }

    public string GenerateRandomEmojis(int count = 3)
    {
        var selectedEmojis = new List<string>();
        var availableEmojis = _emojis.ToList();

        for (int i = 0; i < count && availableEmojis.Count > 0; i++)
        {
            var index = _random.Next(availableEmojis.Count);
            selectedEmojis.Add(availableEmojis[index]);
            availableEmojis.RemoveAt(index); // Avoid duplicates in the same reply
        }

        return string.Join("", selectedEmojis);
    }

    private async Task EnsureWelcomeMessagesLoadedAsync()
    {
        if (_welcomeMessages != null)
            return;

        try
        {
            var configPath = Path.Combine(_hostEnvironment.ContentRootPath, "Configuration", "welcome-messages.json");
            
            if (!File.Exists(configPath))
            {
                _logger.LogWarning("Welcome messages configuration file not found at {ConfigPath}, using defaults", configPath);
                _welcomeMessages = GetDefaultWelcomeMessages();
                return;
            }

            var jsonContent = await File.ReadAllTextAsync(configPath);
            var config = JsonSerializer.Deserialize<WelcomeMessagesConfig>(jsonContent);
            
            _welcomeMessages = config?.WelcomeMessages ?? GetDefaultWelcomeMessages();
            
            _logger.LogInformation("Loaded {Count} welcome messages from configuration", _welcomeMessages.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load welcome messages configuration, using defaults");
            _welcomeMessages = GetDefaultWelcomeMessages();
        }
    }

    private string GetRandomWelcomeMessage()
    {
        if (_welcomeMessages == null || _welcomeMessages.Count == 0)
        {
            return "Welcome";
        }

        var index = _random.Next(_welcomeMessages.Count);
        return _welcomeMessages[index];
    }

    private List<string> GetDefaultWelcomeMessages()
    {
        return new List<string>
        {
            "Welcome", "Hello", "Hi there", "Greetings", "Hey",
            "Welcome aboard", "Nice to meet you", "Great to see you",
            "Thanks for joining us", "Glad you're here"
        };
    }
}

public class WelcomeMessagesConfig
{
    public List<string> WelcomeMessages { get; set; } = new();
}
