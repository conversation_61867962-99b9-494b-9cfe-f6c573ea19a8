const FacebookPostActions = {
    props: {
        post: {
            type: Object,
            required: true
        },
        canSync: {
            type: Boolean,
            default: false
        },
        canCreateCampaign: {
            type: Boolean,
            default: false
        }
    },
    
    emits: [
        'view-on-facebook',
        'create-campaign',
        'view-campaign',
        'sync-post'
    ],
    
    data() {
        return {
            l: null
        };
    },
    
    computed: {
        hasPermalinkUrl() {
            return this.post.permalinkUrl && this.post.permalinkUrl.trim() !== '';
        },
        
        hasActiveCampaign() {
            return this.post.hasActiveCampaign === true;
        }
    },
    
    template: `
        <div class="facebook-post-actions">
            <div class="d-flex gap-1 flex-wrap">
                <!-- View on Facebook Button -->
                <button v-if="hasPermalinkUrl" 
                        type="button" 
                        class="btn btn-outline-secondary btn-sm" 
                        v-on:click="viewOnFacebook"
                        :title="l('Facebook:ViewOnFacebook') || 'View on Facebook'">
                    <i class="fas fa-external-link-alt"></i>
                </button>
                
                <!-- Create Campaign Button -->
                <button v-if="!hasActiveCampaign && canCreateCampaign" 
                        type="button" 
                        class="btn btn-primary btn-sm" 
                        v-on:click="createCampaign">
                    <i class="fas fa-plus me-1"></i>
                    {{ l('Facebook:CreateCampaign') || 'Create Campaign' }}
                </button>
                
                <!-- View Campaign Button -->
                <button v-if="hasActiveCampaign" 
                        type="button" 
                        class="btn btn-info btn-sm" 
                        v-on:click="viewCampaign">
                    <i class="fas fa-eye me-1"></i>
                    {{ l('Facebook:ViewCampaign') || 'View Campaign' }}
                </button>
                
                <!-- Sync Post Button (in dropdown for space) -->
                <div v-if="canSync" class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                            type="button" 
                            :id="'postActions' + post.id"
                            data-bs-toggle="dropdown" 
                            aria-expanded="false">
                        <i class="fas fa-cog"></i>
                    </button>
                    <ul class="dropdown-menu post-actions-dropdown" :aria-labelledby="'postActions' + post.id">
                        <li>
                            <a class="dropdown-item" href="#" v-on:click.prevent="syncPost">
                                <i class="fas fa-sync-alt me-2"></i>
                                {{ l('Facebook:SyncPost') || 'Sync Post' }}
                            </a>
                        </li>
                        <li v-if="hasPermalinkUrl">
                            <a class="dropdown-item" href="#" v-on:click.prevent="viewOnFacebook">
                                <i class="fas fa-external-link-alt me-2"></i>
                                {{ l('Facebook:ViewOnFacebook') || 'View on Facebook' }}
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="#" v-on:click.prevent="copyPostLink">
                                <i class="fas fa-copy me-2"></i>
                                {{ l('Facebook:CopyLink') || 'Copy Link' }}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        viewOnFacebook() {
            if (this.hasPermalinkUrl) {
                this.$emit('view-on-facebook', this.post.permalinkUrl);
            }
        },
        
        createCampaign() {
            this.$emit('create-campaign', this.post.id);
        },
        
        viewCampaign() {
            this.$emit('view-campaign', this.post.id);
        },
        
        syncPost() {
            this.$emit('sync-post', this.post.id);
        },
        
        async copyPostLink() {
            if (this.hasPermalinkUrl) {
                try {
                    await navigator.clipboard.writeText(this.post.permalinkUrl);
                    abp.notify.success(this.l('Facebook:LinkCopied') || 'Link copied to clipboard');
                } catch (error) {
                    console.error('Error copying link:', error);
                    // Fallback for older browsers
                    this.fallbackCopyTextToClipboard(this.post.permalinkUrl);
                }
            }
        },
        
        fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            
            // Avoid scrolling to bottom
            textArea.style.top = '0';
            textArea.style.left = '0';
            textArea.style.position = 'fixed';
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    abp.notify.success(this.l('Facebook:LinkCopied') || 'Link copied to clipboard');
                } else {
                    abp.notify.error(this.l('Facebook:ErrorCopyingLink') || 'Failed to copy link');
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
                abp.notify.error(this.l('Facebook:ErrorCopyingLink') || 'Failed to copy link');
            }
            
            document.body.removeChild(textArea);
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },

    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    }
};
