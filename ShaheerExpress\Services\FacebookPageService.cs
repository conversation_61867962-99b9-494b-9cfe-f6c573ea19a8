using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ShaheerExpress.Entities;
using ShaheerExpress.Permissions;
using ShaheerExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace ShaheerExpress.Services;

[Authorize]
public class FacebookPageService : ApplicationService, IFacebookPageService
{
    private readonly IRepository<FacebookPage, Guid> _facebookPageRepository;
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly ILogger<FacebookPageService> _logger;

    public FacebookPageService(
        IRepository<FacebookPage, Guid> facebookPageRepository,
        IRepository<FacebookUser, Guid> facebookUserRepository,
        FacebookGraphApiService facebookGraphApiService,
        ILogger<FacebookPageService> logger)
    {
        _facebookPageRepository = facebookPageRepository;
        _facebookUserRepository = facebookUserRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _logger = logger;
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ViewPages)]
    public async Task<PagedResultDto<FacebookPageDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var queryable = await _facebookPageRepository.GetQueryableAsync();
        var query = queryable.Where(x => x.FacebookUserId == facebookUser.Id);

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.OrderByDescending(x => x.CreationTime)
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount);

        var pages = await AsyncExecuter.ToListAsync(query);
        var pageDtos = ObjectMapper.Map<List<FacebookPage>, List<FacebookPageDto>>(pages);

        return new PagedResultDto<FacebookPageDto>(totalCount, pageDtos);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ViewPages)]
    public async Task<FacebookPageDto> GetAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(id);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        return ObjectMapper.Map<FacebookPage, FacebookPageDto>(page);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task<FacebookPageDto> CreateAsync(CreateFacebookPageDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var page = new FacebookPage(
            GuidGenerator.Create(),
            input.FacebookPageId,
            facebookUser.Id,
            input.PageName,
            input.PageAccessToken);

        if (!string.IsNullOrEmpty(input.PageProfilePictureUrl))
        {
            page.UpdatePageInfo(input.PageName, input.PageProfilePictureUrl, input.Category ?? string.Empty, input.FollowersCount);
        }

        await _facebookPageRepository.InsertAsync(page);
        return ObjectMapper.Map<FacebookPage, FacebookPageDto>(page);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task<FacebookPageDto> UpdateAsync(Guid id, UpdateFacebookPageDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(id);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        if (!string.IsNullOrEmpty(input.PageName))
        {
            page.UpdatePageInfo(
                input.PageName,
                input.PageProfilePictureUrl ?? page.PageProfilePictureUrl,
                input.Category ?? page.Category,
                input.FollowersCount ?? page.FollowersCount);
        }

        if (!string.IsNullOrEmpty(input.PageAccessToken))
        {
            page.UpdateAccessToken(input.PageAccessToken);
        }

        await _facebookPageRepository.UpdateAsync(page);
        return ObjectMapper.Map<FacebookPage, FacebookPageDto>(page);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task DeleteAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(id);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        await _facebookPageRepository.DeleteAsync(page);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task<List<FacebookPageImportDto>> GetAvailablePagesFromFacebookAsync()
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        try
        {
            var pages = await _facebookGraphApiService.GetUserPagesAsync(facebookUser.AccessToken);

            return pages.Select(p => new FacebookPageImportDto
            {
                FacebookPageId = p.Id,
                PageName = p.Name,
                PageAccessToken = p.AccessToken,
                PageProfilePictureUrl = p.Picture?.Data?.Url,
                Category = p.Category,
                FollowersCount = p.FanCount
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available pages from Facebook");
            throw new UserFriendlyException("Failed to retrieve pages from Facebook. Please check your connection.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task<FacebookPageDto> ImportPageAsync(FacebookPageImportDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        // Check if page already exists
        var existingPage = await _facebookPageRepository.FirstOrDefaultAsync(
            x => x.FacebookPageId == input.FacebookPageId && x.FacebookUserId == facebookUser.Id);

        if (existingPage != null)
        {
            // Update existing page
            existingPage.UpdatePageInfo(input.PageName, input.PageProfilePictureUrl ?? string.Empty,
                input.Category ?? string.Empty, input.FollowersCount);
            existingPage.UpdateAccessToken(input.PageAccessToken);
            existingPage.Reconnect();

            await _facebookPageRepository.UpdateAsync(existingPage);
            return ObjectMapper.Map<FacebookPage, FacebookPageDto>(existingPage);
        }
        else
        {
            // Create new page
            var createDto = new CreateFacebookPageDto
            {
                FacebookPageId = input.FacebookPageId,
                FacebookUserId = facebookUser.Id,
                PageName = input.PageName,
                PageAccessToken = input.PageAccessToken,
                PageProfilePictureUrl = input.PageProfilePictureUrl,
                Category = input.Category,
                FollowersCount = input.FollowersCount
            };

            return await CreateAsync(createDto);
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task<List<FacebookPageDto>> ImportMultiplePagesAsync(List<FacebookPageImportDto> input)
    {
        var result = new List<FacebookPageDto>();

        foreach (var pageImport in input)
        {
            try
            {
                var pageDto = await ImportPageAsync(pageImport);
                result.Add(pageDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing page {PageId}", pageImport.FacebookPageId);
                // Continue with other pages
            }
        }

        return result;
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task SubscribeToWebhookAsync(Guid pageId)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(pageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        try
        {
            var success = await _facebookGraphApiService.SubscribeToWebhookAsync(page.FacebookPageId, page.PageAccessToken);
            if (success)
            {
                page.SetWebhookSubscription($"webhook_{page.FacebookPageId}_{DateTime.UtcNow:yyyyMMddHHmmss}");
                await _facebookPageRepository.UpdateAsync(page);
            }
            else
            {
                throw new UserFriendlyException("Failed to subscribe to webhook.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to webhook for page {PageId}", pageId);
            throw new UserFriendlyException("Failed to subscribe to webhook. Please try again.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task UnsubscribeFromWebhookAsync(Guid pageId)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(pageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        try
        {
            var success = await _facebookGraphApiService.UnsubscribeFromWebhookAsync(page.FacebookPageId, page.PageAccessToken);
            if (success)
            {
                page.RemoveWebhookSubscription();
                await _facebookPageRepository.UpdateAsync(page);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from webhook for page {PageId}", pageId);
            throw new UserFriendlyException("Failed to unsubscribe from webhook. Please try again.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task SyncPageInfoAsync(Guid pageId)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(pageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        try
        {
            var pages = await _facebookGraphApiService.GetUserPagesAsync(facebookUser.AccessToken);
            var pageInfo = pages.FirstOrDefault(p => p.Id == page.FacebookPageId);

            if (pageInfo != null)
            {
                page.UpdatePageInfo(pageInfo.Name, pageInfo.Picture?.Data?.Url ?? string.Empty,
                    pageInfo.Category ?? string.Empty, pageInfo.FanCount);
                page.UpdateAccessToken(pageInfo.AccessToken);

                await _facebookPageRepository.UpdateAsync(page);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing page info for page {PageId}", pageId);
            throw new UserFriendlyException("Failed to sync page information. Please try again.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task SyncAllPagesAsync()
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var pages = await _facebookPageRepository.GetListAsync(x => x.FacebookUserId == facebookUser.Id && x.IsConnected);

        foreach (var page in pages)
        {
            try
            {
                await SyncPageInfoAsync(page.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing page {PageId} during bulk sync", page.Id);
                // Continue with other pages
            }
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task DisconnectPageAsync(Guid pageId)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(pageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        page.Disconnect();
        await _facebookPageRepository.UpdateAsync(page);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task ReconnectPageAsync(Guid pageId)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _facebookPageRepository.GetAsync(pageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        page.Reconnect();
        await _facebookPageRepository.UpdateAsync(page);
    }

    /// <summary>
    /// Refreshes access tokens for all Facebook Pages associated with the current user.
    /// This method fetches fresh page information from Facebook and updates the stored access tokens.
    /// </summary>
    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task RefreshAllPageTokensAsync()
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var facebookUserDto = ObjectMapper.Map<FacebookUser,FacebookUserDto>(facebookUser);
        // Delegate to the new centralized method
        await RefreshAllPageTokensForUserAsync(facebookUserDto);
    }

    private async Task<FacebookUser> GetCurrentUserFacebookUserAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);

        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected. Please connect your Facebook account first.");
        }

        return facebookUser;
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ManagePages)]
    public async Task RefreshAllPageTokensForUserAsync(FacebookUserDto facebookUser)
    {
        _logger.LogInformation("Starting RefreshAllPageTokensForUserAsync for user {UserId} (FacebookUser: {FacebookUserId})",
              facebookUser.UserId, facebookUser.Id);

        try
        {
            var userPages = await _facebookPageRepository.GetListAsync(x => x.FacebookUserId == facebookUser.Id);
            _logger.LogInformation("Found {PageCount} existing pages for user {UserId}", userPages.Count, facebookUser.UserId);

            if (!userPages.Any())
            {
                _logger.LogInformation("No Facebook pages found for user {UserId} to refresh tokens", facebookUser.UserId);
                return;
            }

            var freshPages = await _facebookGraphApiService.GetUserPagesAsync(facebookUser.AccessToken);
            _logger.LogInformation("Retrieved {FreshPageCount} fresh pages from Facebook API for user {UserId}",
                freshPages.Count, facebookUser.UserId);

            var updatedCount = 0;
            var reconnectedCount = 0;

            foreach (var existingPage in userPages)
            {
                var freshPageInfo = freshPages.FirstOrDefault(p => p.Id == existingPage.FacebookPageId);

                if (freshPageInfo != null)
                {
                    if (!string.IsNullOrEmpty(freshPageInfo.AccessToken))
                    {
                        existingPage.UpdateAccessToken(freshPageInfo.AccessToken);
                        _logger.LogInformation("Updated access token for page {PageId}", existingPage.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Fresh page info for {PageId} ({PageName}) has null or empty access token",
                            existingPage.Id, existingPage.PageName);
                    }

                    if (!existingPage.IsConnected)
                    {
                        existingPage.Reconnect();
                        reconnectedCount++;
                    }

                    await _facebookPageRepository.UpdateAsync(existingPage);
                    updatedCount++;
                }
                else
                {
                    _logger.LogWarning("Facebook page {PageId} ({PageName}) with FacebookPageId {FacebookPageId} not found in fresh page list - user may have lost access",
                        existingPage.Id, existingPage.PageName, existingPage.FacebookPageId);
                }
            }

            _logger.LogInformation("Completed RefreshAllPageTokensForUserAsync for user {UserId}. Updated: {UpdatedCount}, Reconnected: {ReconnectedCount} pages.",
                facebookUser.UserId, updatedCount, reconnectedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RefreshAllPageTokensForUserAsync for user {UserId}. Exception: {ExceptionType}, Message: {ErrorMessage}",
                facebookUser.UserId, ex.GetType().Name, ex.Message);
            throw new UserFriendlyException("Failed to refresh page tokens. Please try again or reconnect your Facebook account.");
        }
    }
}
