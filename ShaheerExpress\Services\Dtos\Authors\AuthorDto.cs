using System;
using System.Collections.Generic;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace ShaheerExpress.Authors
{
    public class AuthorDto : FullAuditedEntityDto<Guid>, IHasConcurrencyStamp
    {
        public string Name { get; set; } = null!;
        public DateOnly Birthdate { get; set; }
        public string? Bio { get; set; }

        public string ConcurrencyStamp { get; set; } = null!;

    }
}