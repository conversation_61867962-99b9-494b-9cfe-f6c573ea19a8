﻿using Microsoft.EntityFrameworkCore;
using ShaheerExpress.Entities.Authors;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using YamlDotNet.Core.Tokens;
using System.Linq.Dynamic.Core;

namespace ShaheerExpress.Data.Authors
{
    public class EfCoreAuthorRepository : EfCoreRepository<ShaheerExpressDbContext, Author, Guid>, IAuthorRepository
    {
        public EfCoreAuthorRepository(IDbContextProvider<ShaheerExpressDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public virtual async Task<List<Author>> GetListAsync(
            string? filterText = null,
            string? name = null,
            DateOnly? birthdateMin = null,
            DateOnly? birthdateMax = null,
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, name, birthdateMin, birthdateMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? AuthorConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public virtual async Task<long> GetCountAsync(
            string? filterText = null,
            string? name = null,
            DateOnly? birthdateMin = null,
            DateOnly? birthdateMax = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), filterText, name, birthdateMin, birthdateMax);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<Author> ApplyFilter(
            IQueryable<Author> query,
            string? filterText = null,
            string? name = null,
            DateOnly? birthdateMin = null,
            DateOnly? birthdateMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.Name!.Contains(filterText!))
                    .WhereIf(!string.IsNullOrWhiteSpace(name), e => e.Name.Contains(name))
                    .WhereIf(birthdateMin.HasValue, e => e.Birthdate >= birthdateMin!.Value)
                    .WhereIf(birthdateMax.HasValue, e => e.Birthdate <= birthdateMax!.Value);
        }
    }
}
