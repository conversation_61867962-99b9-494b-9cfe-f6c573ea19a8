using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ShaheerExpress.Services;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace ShaheerExpress.Pages.Facebook.Campaigns
{
    public class CreateModel : AbpPageModel
    {
        protected IAutoReplyCampaignService _campaignService;
        protected IFacebookPostService _facebookPostService;
        protected IFacebookPageService _facebookPageService;
        protected IFacebookAuthService _facebookAuthService;

        [BindProperty(SupportsGet = true)]
        public Guid? PostId { get; set; }

        [BindProperty(SupportsGet = true)]
        public Guid? PageId { get; set; }

        [BindProperty(SupportsGet = true)]
        public Guid? TemplateId { get; set; }

        public CreateModel(
            IAutoReplyCampaignService campaignService,
            IFacebookPostService facebookPostService,
            IFacebookPageService facebookPageService,
            IFacebookAuthService facebookAuthService)
        {
            _campaignService = campaignService;
            _facebookPostService = facebookPostService;
            _facebookPageService = facebookPageService;
            _facebookAuthService = facebookAuthService;
        }

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
