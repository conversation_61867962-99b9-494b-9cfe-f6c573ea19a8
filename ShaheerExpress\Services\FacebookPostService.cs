using System;
using System.Linq;
using System.Threading.Tasks;
using ShaheerExpress.Entities;
using ShaheerExpress.Permissions;
using ShaheerExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace ShaheerExpress.Services;

[Authorize]
public class FacebookPostService : ApplicationService, IFacebookPostService
{
    private readonly IRepository<FacebookPost, Guid> _postRepository;
    private readonly IRepository<FacebookPage, Guid> _pageRepository;
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly IRepository<AutoReplyCampaign, Guid> _campaignRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly ILogger<FacebookPostService> _logger;

    public FacebookPostService(
        IRepository<FacebookPost, Guid> postRepository,
        IRepository<FacebookPage, Guid> pageRepository,
        IRepository<FacebookUser, Guid> facebookUserRepository,
        IRepository<AutoReplyCampaign, Guid> campaignRepository,
        FacebookGraphApiService facebookGraphApiService,
        ILogger<FacebookPostService> logger)
    {
        _postRepository = postRepository;
        _pageRepository = pageRepository;
        _facebookUserRepository = facebookUserRepository;
        _campaignRepository = campaignRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _logger = logger;
    }

    [Authorize(ShaheerExpressPermissions.Posts.View)]
    public async Task<PagedResultDto<FacebookPostDto>> GetListAsync(GetPostsInput input)
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var queryable = await _postRepository.GetQueryableAsync();
        var pageQueryable = await _pageRepository.GetQueryableAsync();
        var campaignQueryable = await _campaignRepository.GetQueryableAsync();

        var query = from post in queryable
                    join page in pageQueryable on post.FacebookPageId equals page.Id
                    where page.FacebookUserId == facebookUser.Id && post.IsActive
                    select new { Post = post, Page = page };

        // Apply filters
        if (input.FacebookPageId.HasValue)
        {
            query = query.Where(x => x.Post.FacebookPageId == input.FacebookPageId.Value);
        }

        if (!string.IsNullOrEmpty(input.SearchText))
        {
            query = query.Where(x => x.Post.Message.Contains(input.SearchText) || x.Page.PageName.Contains(input.SearchText));
        }

        if (input.FromDate.HasValue)
        {
            query = query.Where(x => x.Post.FacebookCreatedTime >= input.FromDate.Value);
        }

        if (input.ToDate.HasValue)
        {
            query = query.Where(x => x.Post.FacebookCreatedTime <= input.ToDate.Value);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply sorting
        if (!string.IsNullOrEmpty(input.Sorting))
        {
            // For now, use default sorting. Dynamic sorting would require System.Linq.Dynamic.Core
            query = query.OrderByDescending(x => x.Post.FacebookCreatedTime);
        }
        else
        {
            query = query.OrderByDescending(x => x.Post.FacebookCreatedTime);
        }

        query = query.Skip(input.SkipCount).Take(input.MaxResultCount);

        var items = await AsyncExecuter.ToListAsync(query);
        var postDtos = new List<FacebookPostDto>();

        foreach (var item in items)
        {
            var postDto = ObjectMapper.Map<FacebookPost, FacebookPostDto>(item.Post);
            postDto.PageName = item.Page.PageName;

            // Check if post has active campaign (check both database and in-memory campaigns)
            var hasActiveCampaign = await _campaignRepository.AnyAsync(c =>
                (c.FacebookPostId == item.Post.Id || c.FacebookPostIdString == item.Post.FacebookPostId) && c.IsActive);
            postDto.HasActiveCampaign = hasActiveCampaign;

            postDtos.Add(postDto);
        }

        // Apply campaign filter if specified
        if (input.HasActiveCampaign.HasValue)
        {
            postDtos = postDtos.Where(p => p.HasActiveCampaign == input.HasActiveCampaign.Value).ToList();
        }

        return new PagedResultDto<FacebookPostDto>(totalCount, postDtos);
    }

    [Authorize(ShaheerExpressPermissions.Posts.View)]
    public async Task<FacebookPostDto> GetAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var post = await _postRepository.GetAsync(id);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        var postDto = ObjectMapper.Map<FacebookPost, FacebookPostDto>(post);
        postDto.PageName = page.PageName;

        var hasActiveCampaign = await _campaignRepository.AnyAsync(c =>
            (c.FacebookPostId == post.Id || c.FacebookPostIdString == post.FacebookPostId) && c.IsActive);
        postDto.HasActiveCampaign = hasActiveCampaign;

        return postDto;
    }

    [Authorize(ShaheerExpressPermissions.Posts.Sync)]
    public async Task<FacebookPostDto> CreateAsync(CreateFacebookPostDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _pageRepository.GetAsync(input.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        var post = new FacebookPost(
            GuidGenerator.Create(),
            input.FacebookPostId,
            input.FacebookPageId,
            input.Message,
            input.PostType,
            input.FacebookCreatedTime);

        post.UpdatePostStats(input.LikesCount, input.CommentsCount, input.SharesCount);

        if (!string.IsNullOrEmpty(input.AttachmentUrl))
        {
            post.UpdateAttachment(input.AttachmentUrl);
        }

        if (!string.IsNullOrEmpty(input.LinkUrl))
        {
            post.UpdateLink(input.LinkUrl);
        }

        await _postRepository.InsertAsync(post);

        var postDto = ObjectMapper.Map<FacebookPost, FacebookPostDto>(post);
        postDto.PageName = page.PageName;
        postDto.HasActiveCampaign = false;

        return postDto;
    }

    [Authorize(ShaheerExpressPermissions.Posts.Sync)]
    public async Task<FacebookPostDto> UpdateAsync(Guid id, UpdateFacebookPostDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var post = await _postRepository.GetAsync(id);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        if (!string.IsNullOrEmpty(input.Message))
        {
            // Note: We typically don't update the message as it comes from Facebook
        }

        if (input.LikesCount.HasValue || input.CommentsCount.HasValue || input.SharesCount.HasValue)
        {
            post.UpdatePostStats(
                input.LikesCount ?? post.LikesCount,
                input.CommentsCount ?? post.CommentsCount,
                input.SharesCount ?? post.SharesCount);
        }

        if (!string.IsNullOrEmpty(input.AttachmentUrl))
        {
            post.UpdateAttachment(input.AttachmentUrl);
        }

        if (!string.IsNullOrEmpty(input.LinkUrl))
        {
            post.UpdateLink(input.LinkUrl);
        }

        await _postRepository.UpdateAsync(post);

        var postDto = ObjectMapper.Map<FacebookPost, FacebookPostDto>(post);
        postDto.PageName = page.PageName;

        var hasActiveCampaign = await _campaignRepository.AnyAsync(c =>
            c.FacebookPostId == post.Id && c.IsActive);
        postDto.HasActiveCampaign = hasActiveCampaign;

        return postDto;
    }

    [Authorize(ShaheerExpressPermissions.Posts.Sync)]
    public async Task DeleteAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var post = await _postRepository.GetAsync(id);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        post.Deactivate();
        await _postRepository.UpdateAsync(post);
    }

    [Authorize(ShaheerExpressPermissions.Posts.Sync)]
    public async Task SyncPostsFromPageAsync(Guid pageId)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var page = await _pageRepository.GetAsync(pageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        try
        {
            var posts = await _facebookGraphApiService.GetPagePostsAsync(page.FacebookPageId, page.PageAccessToken);

            foreach (var postInfo in posts)
            {
                var existingPost = await _postRepository.FirstOrDefaultAsync(p => p.FacebookPostId == postInfo.Id);

                if (existingPost != null)
                {
                    // Update existing post
                    existingPost.UpdatePostStats(
                        postInfo.Likes?.Summary?.TotalCount ?? 0,
                        postInfo.Comments?.Summary?.TotalCount ?? 0,
                        postInfo.Shares?.Count ?? 0);

                    // Update new fields if they're not already set
                    if (string.IsNullOrEmpty(existingPost.PermalinkUrl) && !string.IsNullOrEmpty(postInfo.PermalinkUrl))
                    {
                        existingPost.UpdatePermalinkUrl(postInfo.PermalinkUrl);
                    }

                    if (string.IsNullOrEmpty(existingPost.PictureUrl) || string.IsNullOrEmpty(existingPost.FullPictureUrl))
                    {
                        existingPost.UpdatePictureUrls(postInfo.Picture, postInfo.FullPicture);
                    }

                    await _postRepository.UpdateAsync(existingPost);
                }
                else
                {
                    // Create new post - determine post type from content
                    var postType = DeterminePostType(postInfo);
                    var newPost = new FacebookPost(
                        GuidGenerator.Create(),
                        postInfo.Id,
                        page.Id,
                        postInfo.Message ?? string.Empty,
                        postType,
                        postInfo.CreatedTime);

                    newPost.UpdatePostStats(
                        postInfo.Likes?.Summary?.TotalCount ?? 0,
                        postInfo.Comments?.Summary?.TotalCount ?? 0,
                        postInfo.Shares?.Count ?? 0);

                    if (postInfo.Attachments?.Data?.Any() == true)
                    {
                        var attachment = postInfo.Attachments.Data.First();
                        if (attachment.Media?.Image?.Src != null)
                        {
                            newPost.UpdateAttachment(attachment.Media.Image.Src);
                        }
                    }

                    // Update new fields
                    if (!string.IsNullOrEmpty(postInfo.PermalinkUrl))
                    {
                        newPost.UpdatePermalinkUrl(postInfo.PermalinkUrl);
                    }

                    newPost.UpdatePictureUrls(postInfo.Picture, postInfo.FullPicture);

                    await _postRepository.InsertAsync(newPost);
                }
            }

            page.LastSyncAt = DateTime.UtcNow;
            await _pageRepository.UpdateAsync(page);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing posts for page {PageId}", pageId);
            throw new UserFriendlyException("Failed to sync posts. Please try again.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Posts.Sync)]
    public async Task SyncAllPostsAsync()
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var pages = await _pageRepository.GetListAsync(p => p.FacebookUserId == facebookUser.Id && p.IsConnected);

        foreach (var page in pages)
        {
            try
            {
                await SyncPostsFromPageAsync(page.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing posts for page {PageId} during bulk sync", page.Id);
                // Continue with other pages
            }
        }
    }

    [Authorize(ShaheerExpressPermissions.Posts.View)]
    public async Task<PagedResultDto<FacebookPostDto>> GetPostsForCampaignCreationAsync(GetPostsInput input)
    {
        // This method returns posts that don't have active campaigns
        input.HasActiveCampaign = false;
        return await GetListAsync(input);
    }

    [Authorize(ShaheerExpressPermissions.Posts.View)]
    public async Task<List<FacebookPostSelectionDto>> GetPostsForSelectionAsync(GetPostsForSelectionInput input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var posts = new List<FacebookPostSelectionDto>();

        try
        {
            // Get user's connected Facebook pages
            var pagesQuery = await _pageRepository.GetQueryableAsync();
            var userPages = await AsyncExecuter.ToListAsync(
                pagesQuery.Where(p => p.FacebookUserId == facebookUser.Id && p.IsConnected));

            if (!userPages.Any())
            {
                _logger.LogWarning("No connected Facebook pages found for user {UserId}", facebookUser.UserId);
                return posts;
            }

            // Filter by specific page if requested
            if (input.FacebookPageId.HasValue)
            {
                userPages = userPages.Where(p => p.Id == input.FacebookPageId.Value).ToList();
            }

            // Get existing campaigns to check for active campaigns
            var campaignQuery = await _campaignRepository.GetQueryableAsync();
            var activeCampaigns = await AsyncExecuter.ToListAsync(
                campaignQuery.Where(c => c.IsActive));

            // Create a set of active campaign post IDs (both database and in-memory)
            var activeCampaignPostIds = new HashSet<string>();
            foreach (var campaign in activeCampaigns)
            {
                if (!string.IsNullOrEmpty(campaign.FacebookPostIdString))
                {
                    activeCampaignPostIds.Add(campaign.FacebookPostIdString);
                }
            }

            // Fetch both posts and video reels from each page using Facebook Graph API
            var allPostTasks = userPages.Select(async page =>
            {
                try
                {
                    var postsPerPage = Math.Min(input.Limit / userPages.Count, 50); // Distribute limit across pages, max 50 per page
                    var halfLimit = Math.Max(1, postsPerPage / 2); // Split between posts and reels

                    // Fetch regular posts and video reels in parallel
                    var postsTask = _facebookGraphApiService.GetPagePostsAsync(
                        page.FacebookPageId, page.PageAccessToken, halfLimit);
                    var reelsTask = _facebookGraphApiService.GetPageVideoReelsAsync(
                        page.FacebookPageId, page.PageAccessToken, halfLimit);

                    await Task.WhenAll(postsTask, reelsTask);

                    var facebookPosts = await postsTask;
                    var facebookReels = await reelsTask;

                    var allContent = new List<FacebookPostSelectionDto>();

                    // Convert regular posts
                    allContent.AddRange(facebookPosts.Select(fbPost => new FacebookPostSelectionDto
                    {
                        FacebookPostId = fbPost.Id,
                        FacebookPageId = page.FacebookPageId,
                        PageName = page.PageName,
                        PageProfilePictureUrl = page.PageProfilePictureUrl,
                        Message = fbPost.Message ?? string.Empty,
                        PostType = DeterminePostType(fbPost),
                        PermalinkUrl = fbPost.PermalinkUrl,
                        PictureUrl = fbPost.Picture,
                        FullPictureUrl = fbPost.FullPicture,
                        FacebookCreatedTime = fbPost.CreatedTime,
                        LikesCount = fbPost.Likes?.Summary?.TotalCount ?? 0,
                        CommentsCount = fbPost.Comments?.Summary?.TotalCount ?? 0,
                        SharesCount = fbPost.Shares?.Count ?? 0,
                        HasActiveCampaign = activeCampaignPostIds.Contains(fbPost.Id)
                    }));

                    // Convert video reels
                    allContent.AddRange(facebookReels.Select(fbReel => new FacebookPostSelectionDto
                    {
                        FacebookPostId = $"{page.FacebookPageId}_{fbReel.PostId}",
                        FacebookPageId = page.FacebookPageId,
                        PageName = page.PageName,
                        PageProfilePictureUrl = page.PageProfilePictureUrl,
                        Message = fbReel.Description ?? string.Empty,
                        PostType = "video_reel",
                        PermalinkUrl = fbReel.PermalinkUrl,
                        PictureUrl = fbReel.Picture ?? fbReel.Thumbnails?.Data?.FirstOrDefault()?.Uri,
                        FullPictureUrl = fbReel.Picture ?? fbReel.Thumbnails?.Data?.FirstOrDefault()?.Uri,
                        FacebookCreatedTime = fbReel.CreatedTime,
                        LikesCount = fbReel.Likes?.Summary?.TotalCount ?? 0,
                        CommentsCount = fbReel.Comments?.Summary?.TotalCount ?? 0,
                        SharesCount = fbReel.Shares?.Count ?? 0,
                        HasActiveCampaign = activeCampaignPostIds.Contains($"{page.FacebookPageId}_{fbReel.PostId}")
                    }));

                    return allContent;
                }
                catch (HttpRequestException httpEx) when (httpEx.Message.Contains("401") || httpEx.Message.Contains("403"))
                {
                    _logger.LogWarning("Facebook access token expired or invalid for page {PageId} ({PageName}). User needs to reconnect.",
                        page.FacebookPageId, page.PageName);

                    // Mark page as disconnected
                    page.IsConnected = false;
                    await _pageRepository.UpdateAsync(page);

                    return new List<FacebookPostSelectionDto>();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to fetch posts and reels for page {PageId} ({PageName})",
                        page.FacebookPageId, page.PageName);
                    return new List<FacebookPostSelectionDto>();
                }
            });

            var pagePostResults = await Task.WhenAll(allPostTasks);
            posts = pagePostResults.SelectMany(p => p)
                .OrderByDescending(p => p.FacebookCreatedTime)
                .Take(input.Limit)
                .ToList();

            _logger.LogInformation("Successfully fetched {PostCount} posts for selection from {PageCount} pages",
                posts.Count, userPages.Count);

            // Check if any pages were disconnected during the process
            var disconnectedPages = userPages.Where(p => !p.IsConnected).ToList();
            if (disconnectedPages.Any())
            {
                var pageNames = string.Join(", ", disconnectedPages.Select(p => p.PageName));
                _logger.LogWarning("Some Facebook pages were disconnected during post fetching: {PageNames}", pageNames);

                if (posts.Count == 0)
                {
                    throw new UserFriendlyException(
                        $"Unable to fetch posts. The following Facebook pages need to be reconnected: {pageNames}. " +
                        "Please go to Facebook Connection page to reconnect your account.");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching posts for selection");
            throw new UserFriendlyException("Failed to fetch Facebook posts. Please check your Facebook connection and try again.");
        }

        return posts;
    }

    [Authorize(ShaheerExpressPermissions.Campaigns.Create)]
    public async Task<List<FacebookPostSelectionDto>> GetLivePostsForCampaignCreationAsync(GetLivePostsForCampaignInput input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        // Get user's connected pages
        var userPages = await _pageRepository.GetListAsync(p => p.FacebookUserId == facebookUser.Id && p.IsConnected);

        if (!userPages.Any())
        {
            _logger.LogWarning("No connected Facebook pages found for user {UserId}", facebookUser.Id);
            return new List<FacebookPostSelectionDto>();
        }

        // Filter pages if specific page is requested
        if (input.FacebookPageId.HasValue)
        {
            var pageEntity = await _pageRepository.GetAsync(input.FacebookPageId.Value);
            userPages = userPages.Where(p => p.FacebookPageId == pageEntity.FacebookPageId).ToList();
        }

        // Get active campaign post IDs to exclude them
        var activeCampaignPostIds = await GetActiveCampaignPostIdsAsync();

        // Fetch posts and reels from Facebook Graph API for each page
        var allPostTasks = userPages.Select(async page =>
        {
            try
            {
                var postsPerPage = Math.Min(input.Limit / userPages.Count, 50); // Distribute limit across pages, max 50 per page
                var halfLimit = Math.Max(1, postsPerPage / 2); // Split between posts and reels

                // Fetch regular posts and video reels in parallel
                var postsTask = _facebookGraphApiService.GetPagePostsAsync(
                    page.FacebookPageId, page.PageAccessToken, halfLimit);
                var reelsTask = _facebookGraphApiService.GetPageVideoReelsAsync(
                    page.FacebookPageId, page.PageAccessToken, halfLimit);

                var (fbPosts, fbReels) = await Task.WhenAll(postsTask, reelsTask);

                var allContent = new List<FacebookPostSelectionDto>();

                // Convert regular posts
                allContent.AddRange(fbPosts.Select(fbPost => new FacebookPostSelectionDto
                {
                    FacebookPostId = fbPost.Id,
                    FacebookPageId = page.FacebookPageId,
                    PageName = page.PageName,
                    PageProfilePictureUrl = page.PageProfilePictureUrl,
                    Message = fbPost.Message ?? string.Empty,
                    PostType = DeterminePostType(fbPost),
                    PermalinkUrl = fbPost.PermalinkUrl,
                    PictureUrl = fbPost.Picture ?? fbPost.FullPicture,
                    FullPictureUrl = fbPost.FullPicture ?? fbPost.Picture,
                    FacebookCreatedTime = fbPost.CreatedTime,
                    LikesCount = fbPost.Likes?.Summary?.TotalCount ?? 0,
                    CommentsCount = fbPost.Comments?.Summary?.TotalCount ?? 0,
                    SharesCount = fbPost.Shares?.Count ?? 0,
                    HasActiveCampaign = activeCampaignPostIds.Contains($"{page.FacebookPageId}_{fbPost.Id}")
                }));

                // Convert video reels
                allContent.AddRange(fbReels.Select(fbReel => new FacebookPostSelectionDto
                {
                    FacebookPostId = fbReel.PostId,
                    FacebookPageId = page.FacebookPageId,
                    PageName = page.PageName,
                    PageProfilePictureUrl = page.PageProfilePictureUrl,
                    Message = fbReel.Description ?? fbReel.Title ?? string.Empty,
                    PostType = FacebookPostTypes.VideoReel,
                    PermalinkUrl = fbReel.PermalinkUrl,
                    PictureUrl = fbReel.Picture ?? fbReel.Thumbnails?.Data?.FirstOrDefault()?.Uri,
                    FullPictureUrl = fbReel.Picture ?? fbReel.Thumbnails?.Data?.FirstOrDefault()?.Uri,
                    FacebookCreatedTime = fbReel.CreatedTime,
                    LikesCount = fbReel.Likes?.Summary?.TotalCount ?? 0,
                    CommentsCount = fbReel.Comments?.Summary?.TotalCount ?? 0,
                    SharesCount = fbReel.Shares?.Count ?? 0,
                    HasActiveCampaign = activeCampaignPostIds.Contains($"{page.FacebookPageId}_{fbReel.PostId}")
                }));

                return allContent;
            }
            catch (HttpRequestException httpEx) when (httpEx.Message.Contains("401") || httpEx.Message.Contains("403"))
            {
                _logger.LogWarning("Facebook access token expired or invalid for page {PageId} ({PageName}). User needs to reconnect.",
                    page.FacebookPageId, page.PageName);

                // Mark page as disconnected
                page.IsConnected = false;
                await _pageRepository.UpdateAsync(page);

                return new List<FacebookPostSelectionDto>();
            }
            catch (HttpRequestException httpEx) when (httpEx.Message.Contains("429"))
            {
                _logger.LogWarning("Facebook API rate limit exceeded for page {PageId} ({PageName}). Skipping this page.",
                    page.FacebookPageId, page.PageName);

                return new List<FacebookPostSelectionDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch live posts and reels for page {PageId} ({PageName})",
                    page.FacebookPageId, page.PageName);
                return new List<FacebookPostSelectionDto>();
            }
        });

        var allPosts = (await Task.WhenAll(allPostTasks)).SelectMany(posts => posts).ToList();

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(input.SearchText))
        {
            var searchText = input.SearchText.ToLowerInvariant();
            allPosts = allPosts.Where(p =>
                p.Message.ToLowerInvariant().Contains(searchText) ||
                p.PageName.ToLowerInvariant().Contains(searchText)
            ).ToList();
        }

        // Filter out posts with active campaigns (for campaign creation)
        allPosts = allPosts.Where(p => !p.HasActiveCampaign).ToList();

        // Sort by creation time (newest first)
        return allPosts.OrderByDescending(p => p.FacebookCreatedTime).Take(input.Limit).ToList();
    }

    private async Task<HashSet<string>> GetActiveCampaignPostIdsAsync()
    {
        try
        {
            // Get all active campaigns and their associated post IDs
            var activeCampaigns = await _campaignRepository.GetListAsync(c => c.IsActive);
            var postIds = new HashSet<string>();

            foreach (var campaign in activeCampaigns)
            {
                if (!string.IsNullOrEmpty(campaign.FacebookPostId))
                {
                    // Create a composite key of pageId_postId for uniqueness
                    postIds.Add($"{campaign.FacebookPageId}_{campaign.FacebookPostId}");
                }
            }

            return postIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active campaign post IDs");
            return new HashSet<string>();
        }
    }



    private async Task<FacebookUser> GetCurrentUserFacebookUserAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);

        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected. Please connect your Facebook account first.");
        }

        return facebookUser;
    }

    private static string DeterminePostType(FacebookPostInfo postInfo)
    {
        // Determine post type based on available data since 'type' field is deprecated
        if (postInfo.Attachments?.Data?.Any() == true)
        {
            var attachment = postInfo.Attachments.Data.First();
            if (attachment.Type != null)
            {
                return attachment.Type.ToLower() switch
                {
                    "photo" => "photo",
                    "video" => "video",
                    "album" => "album",
                    _ => "link"
                };
            }
        }

        if (!string.IsNullOrEmpty(postInfo.Picture) || !string.IsNullOrEmpty(postInfo.FullPicture))
        {
            return "photo";
        }

        if (!string.IsNullOrEmpty(postInfo.Message))
        {
            return "status";
        }

        return "status"; // Default fallback
    }
}
