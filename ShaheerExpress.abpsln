{
  "id": "1c05c852-66c2-45d7-b45a-9bcb56cc5697",
  "template": "app-nolayers",
  "versions": {
    "LeptonX": "4.2.1",
    "AbpFramework": "9.2.1",
    "AbpCommercial": "9.2.1",
    "AbpStudio": "1.0.2",
    "TargetDotnetFramework": "net9.0"
  },
  "modules": {
    "ShaheerExpress": {
      "path": "ShaheerExpress.abpmdl"
    }
  },
  "runProfiles": {
    "Default": {
      "path": "etc/run-profiles/Default.abprun.json"
    }
  },
  "options": {
    "httpRequests": {
      "ignoredUrls": [
      
      ]
    }
  },
  "creatingStudioConfiguration": {
    "template": "app-nolayers",
    "createdAbpStudioVersion": "1.0.2",
    "multiTenancy": "true",
    "runInstallLibs": "true",
    "useLocalReferences": "false",
    "uiFramework": "mvc",
    "databaseProvider": "ef",
    "runDbMigrator": "true",
    "databaseManagementSystem": "sqlserver",
    "createInitialMigration": "true",
    "theme": "leptonx",
    "themeStyle": "dim",
    "themeMenuPlacement": "side",
    "publicWebsite": "",
    "optionalModules": " TextTemplateManagement LanguageManagement AuditLogging OpenIddictAdmin",
    "socialLogin": "true",
    "selectedLanguages": ["العربية", "English", ],
    "defaultLanguage": "العربية",
    "createCommand": "abp new ShaheerExpress -t app-nolayers --ui-framework mvc --mobile  --database-provider ef --database-management-system sqlserver --theme leptonx --no-tests --without-cms-kit --dont-run-bundling -no-gdpr -no-file-management"
  }
}