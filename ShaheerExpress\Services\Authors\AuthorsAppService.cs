﻿using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Authorization;
using ShaheerExpress.Authors;
using ShaheerExpress.Data.Authors;
using ShaheerExpress.Entities.Authors;
using ShaheerExpress.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace ShaheerExpress.Services.Authors
{
    [Authorize(ShaheerExpressPermissions.Authors.Default)]
    public class AuthorsAppService : ApplicationService, IAuthorsAppService
    {

        protected IAuthorRepository _authorRepository;
        protected AuthorManager _authorManager;

        public AuthorsAppService(IAuthorRepository authorRepository, AuthorManager authorManager)
        {

            _authorRepository = authorRepository;
            _authorManager = authorManager;

        }

        public virtual async Task<PagedResultDto<AuthorDto>> GetListAsync(GetAuthorsInput input)
        {
            var totalCount = await _authorRepository.GetCountAsync(input.FilterText, input.Name, input.BirthdateMin, input.BirthdateMax);
            var items = await _authorRepository.GetListAsync(input.FilterText, input.Name, input.BirthdateMin, input.BirthdateMax, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<AuthorDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<Author>, List<AuthorDto>>(items)
            };
        }

        public virtual async Task<AuthorDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<Author, AuthorDto>(await _authorRepository.GetAsync(id));
        }

        [Authorize(ShaheerExpressPermissions.Authors.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _authorRepository.DeleteAsync(id);
        }

        [Authorize(ShaheerExpressPermissions.Authors.Create)]
        public virtual async Task<AuthorDto> CreateAsync(AuthorCreateDto input)
        {

            var author = await _authorManager.CreateAsync(
            input.Name, input.Birthdate, input.Bio
            );

            return ObjectMapper.Map<Author, AuthorDto>(author);
        }

        [Authorize(ShaheerExpressPermissions.Authors.Edit)]
        public virtual async Task<AuthorDto> UpdateAsync(Guid id, AuthorUpdateDto input)
        {

            var author = await _authorManager.UpdateAsync(
            id,
            input.Name, input.Birthdate, input.Bio, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<Author, AuthorDto>(author);
        }
    }
}
