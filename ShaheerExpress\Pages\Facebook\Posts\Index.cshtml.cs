using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ShaheerExpress.Services;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace ShaheerExpress.Pages.Facebook.Posts
{
    public class IndexModel : AbpPageModel
    {
        protected IFacebookPostService _facebookPostService;
        protected IFacebookPageService _facebookPageService;
        protected IFacebookAuthService _facebookAuthService;

        public IndexModel(
            IFacebookPostService facebookPostService,
            IFacebookPageService facebookPageService,
            IFacebookAuthService facebookAuthService)
        {
            _facebookPostService = facebookPostService;
            _facebookPageService = facebookPageService;
            _facebookAuthService = facebookAuthService;
        }

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
