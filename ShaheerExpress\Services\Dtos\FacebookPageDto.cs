using System;
using Volo.Abp.Application.Dtos;

namespace ShaheerExpress.Services.Dtos;

public class FacebookPageDto : FullAuditedEntityDto<Guid>
{
    public string FacebookPageId { get; set; } = string.Empty;
    public Guid FacebookUserId { get; set; }
    public string PageName { get; set; } = string.Empty;
    public string PageProfilePictureUrl { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int FollowersCount { get; set; }
    public bool IsConnected { get; set; }
    public bool WebhookSubscribed { get; set; }
    public DateTime? LastSyncAt { get; set; }
}

public class CreateFacebookPageDto
{
    public string FacebookPageId { get; set; } = string.Empty;
    public Guid FacebookUserId { get; set; }
    public string PageName { get; set; } = string.Empty;
    public string PageAccessToken { get; set; } = string.Empty;
    public string? PageProfilePictureUrl { get; set; }
    public string? Category { get; set; }
    public int FollowersCount { get; set; }
}

public class UpdateFacebookPageDto
{
    public string? PageName { get; set; }
    public string? PageProfilePictureUrl { get; set; }
    public string? Category { get; set; }
    public int? FollowersCount { get; set; }
    public string? PageAccessToken { get; set; }
}

public class FacebookPageImportDto
{
    public string FacebookPageId { get; set; } = string.Empty;
    public string PageName { get; set; } = string.Empty;
    public string PageAccessToken { get; set; } = string.Empty;
    public string? PageProfilePictureUrl { get; set; }
    public string? Category { get; set; }
    public int FollowersCount { get; set; }
}
