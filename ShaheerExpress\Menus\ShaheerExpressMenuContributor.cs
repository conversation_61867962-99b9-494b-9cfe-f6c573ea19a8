using ShaheerExpress.Permissions;
using ShaheerExpress.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Identity.Web.Navigation;
using Volo.Abp.SettingManagement.Web.Navigation;
using Volo.Abp.UI.Navigation;
using Volo.Abp.AuditLogging.Web.Navigation;
using Volo.Abp.LanguageManagement.Navigation;
using Volo.Abp.OpenIddict.Pro.Web.Menus;
using Volo.Abp.TextTemplateManagement.Web.Navigation;
using Volo.Saas.Host.Navigation;

namespace ShaheerExpress.Menus;

public class ShaheerExpressMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private static Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var l = context.GetLocalizer<ShaheerExpressResource>();
        context.Menu.Items.Insert(
            0,
            new ApplicationMenuItem(
                ShaheerExpressMenus.Home,
                l["Menu:Home"],
                "~/",
                icon: "fas fa-home",
                order: 0
            )
        );


        //HostDashboard
        context.Menu.AddItem(
            new ApplicationMenuItem(
                ShaheerExpressMenus.HostDashboard,
                l["Menu:Dashboard"],
                "~/HostDashboard",
                icon: "fa fa-chart-line",
                order: 2
            ).RequirePermissions(ShaheerExpressPermissions.Dashboard.Host)
        );

        //TenantDashboard
        context.Menu.AddItem(
            new ApplicationMenuItem(
                ShaheerExpressMenus.TenantDashboard,
                l["Menu:Dashboard"],
                "~/Dashboard",
                icon: "fa fa-chart-line",
                order: 2
            ).RequirePermissions(ShaheerExpressPermissions.Dashboard.Tenant)
        );
        

        //Administration
        var administration = context.Menu.GetAdministration();
        administration.Order = 5;
        
        context.Menu.SetSubItemOrder(SaasHostMenuNames.GroupName, 3);
        
        //Administration->OpenIddict
        administration.SetSubItemOrder(OpenIddictProMenus.GroupName, 3);

        //Administration->Language Management
        administration.SetSubItemOrder(LanguageManagementMenuNames.GroupName,4);
        
        //Administration->Text Template Management
        administration.SetSubItemOrder(TextTemplateManagementMainMenuNames.GroupName, 5);

        //Administration->Audit Logs
        administration.SetSubItemOrder(AbpAuditLoggingMainMenuNames.GroupName, 6);
        //Administration->Identity
        administration.SetSubItemOrder(IdentityMenuNames.GroupName, 2);

        //Administration->Settings
        administration.SetSubItemOrder(SettingManagementMenuNames.GroupName, 7);


        context.Menu.AddItem(
            new ApplicationMenuItem(
                ShaheerExpressMenus.Authors,
                l["Menu:Authors"],
                url: "/Authors",
                icon: "fa fa-file-alt",
                requiredPermissionName: ShaheerExpressPermissions.Authors.Default)
        );

        // Facebook Menu
        context.Menu.AddItem(
            new ApplicationMenuItem(
                ShaheerExpressMenus.Facebook,
                l["Menu:Facebook"],
                icon: "fab fa-facebook",
                order: 4
            )
            .AddItem(new ApplicationMenuItem(
                ShaheerExpressMenus.FacebookConnection,
                l["Menu:Facebook:Connection"],
                "/Facebook/Connection",
                icon: "fas fa-link"
            ).RequirePermissions(ShaheerExpressPermissions.Facebook.Connect))
            .AddItem(new ApplicationMenuItem(
                ShaheerExpressMenus.FacebookPages,
                l["Menu:Facebook:Pages"],
                "/Facebook/Pages",
                icon: "fas fa-file-alt"
            ).RequirePermissions(ShaheerExpressPermissions.Facebook.ViewPages))
        );

        return Task.CompletedTask;
    }
}
