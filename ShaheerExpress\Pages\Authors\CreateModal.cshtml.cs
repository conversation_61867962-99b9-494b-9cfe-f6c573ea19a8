using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ShaheerExpress.Authors;
using ShaheerExpress.Services.Authors;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace ShaheerExpress.Pages.Authors
{
    public class CreateModalModel : AbpPageModel
    {
        [BindProperty]
        public AuthorCreateViewModel Author { get; set; }

        protected IAuthorsAppService _authorsAppService;

        public CreateModalModel(IAuthorsAppService authorsAppService)
        {
            _authorsAppService = authorsAppService;

            Author = new();
        }

        public virtual async Task OnGetAsync()
        {
            Author = new AuthorCreateViewModel();

            await Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnPostAsync()
        {

            await _authorsAppService.CreateAsync(ObjectMapper.Map<AuthorCreateViewModel, AuthorCreateDto>(Author));
            return NoContent();
        }
    }

    public class AuthorCreateViewModel : AuthorCreateDto
    {
    }

}
