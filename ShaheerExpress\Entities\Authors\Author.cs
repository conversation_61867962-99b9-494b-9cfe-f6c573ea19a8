﻿using JetBrains.Annotations;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp;

namespace ShaheerExpress.Entities.Authors
{
    public class Author : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string Name { get; set; }

        public virtual DateOnly Birthdate { get; set; }

        [CanBeNull]
        public virtual string? Bio { get; set; }

        protected Author()
        {

        }

        public Author(Guid id, string name, DateOnly birthdate, string? bio = null)
        {

            Id = id;
            Check.NotNull(name, nameof(name));
            Name = name;
            Birthdate = birthdate;
            Bio = bio;
        }

    }
}
