using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using ShaheerExpress.Services.Dtos;
using Volo.Abp.Domain.Entities.Auditing;

namespace ShaheerExpress.Entities;

public class AutoReplyCampaign : FullAuditedAggregateRoot<Guid>
{
    // Optional for backward compatibility - only used for campaigns created from database posts
    public Guid? FacebookPostId { get; set; }

    [Required]
    [StringLength(256)]
    public string CampaignName { get; set; } = string.Empty;

    [StringLength(1024)]
    public string? Description { get; set; }

    [Required]
    [StringLength(2048)]
    public string PublicReplyMessage { get; set; } = string.Empty;

    [StringLength(2048)]
    public string? PrivateReplyMessage { get; set; }

    public bool IsActive { get; set; } = true;

    public bool SendPublicReply { get; set; } = true;

    public bool SendPrivateReply { get; set; } = false;

    public bool SendLike { get; set; } = false;

    public DateTime? EndDate { get; set; }

    public int MaxRepliesPerUser { get; set; } = 0; // 0 = unlimited replies, > 0 = limit replies per user

    public int TotalRepliesSent { get; set; } = 0;

    public int PublicRepliesSent { get; set; } = 0;

    public int PrivateRepliesSent { get; set; } = 0;

    public int LikesSent { get; set; } = 0;

    public DateTime? LastReplyAt { get; set; }

    // Minimal post metadata stored directly in campaign (for in-memory posts)
    [Required]
    [StringLength(256)]
    public string FacebookPostIdString { get; set; } = string.Empty;

    [Required]
    [StringLength(256)]
    public string FacebookPageIdString { get; set; } = string.Empty;

    [Required]
    [StringLength(256)]
    public string PageName { get; set; } = string.Empty;

    [StringLength(2048)]
    public string PostContent { get; set; } = string.Empty;

    [StringLength(1024)]
    public string? PostPermalinkUrl { get; set; }

    [StringLength(1024)]
    public string? PostPictureUrl { get; set; }

    public DateTime PostCreatedTime { get; set; }

    public bool IncludePostLinkInPrivateReply { get; set; } = false;

    // Reply Type Configuration
    public PublicReplyType PublicReplyType { get; set; } = PublicReplyType.Custom;
    public PrivateReplyType PrivateReplyType { get; set; } = PrivateReplyType.TextReply;

    [StringLength(4000)]
    public string? CardReplyDataJson { get; set; }

    // Navigation property
    public virtual FacebookPost? FacebookPost { get; set; }

    protected AutoReplyCampaign()
    {
        // For EF Core
    }

    public AutoReplyCampaign(
        Guid id,
        Guid facebookPostId,
        string campaignName,
        string publicReplyMessage) : base(id)
    {
        FacebookPostId = facebookPostId;
        CampaignName = campaignName;
        PublicReplyMessage = publicReplyMessage;
        IsActive = true;
        SendPublicReply = true;
        SendPrivateReply = false;
        MaxRepliesPerUser = 0;
        TotalRepliesSent = 0;
        PublicRepliesSent = 0;
        PrivateRepliesSent = 0;
    }

    // Constructor for creating campaigns from in-memory posts
    public AutoReplyCampaign(
        Guid id,
        string facebookPostId,
        string facebookPageId,
        string pageName,
        string postContent,
        DateTime postCreatedTime,
        string campaignName,
        string publicReplyMessage) : base(id)
    {
        FacebookPostId = null; // No database post reference for in-memory posts
        FacebookPostIdString = facebookPostId;
        FacebookPageIdString = facebookPageId;
        PageName = pageName;
        PostContent = postContent;
        PostCreatedTime = postCreatedTime;
        CampaignName = campaignName;
        PublicReplyMessage = publicReplyMessage;
        IsActive = true;
        SendPublicReply = true;
        SendPrivateReply = false;
        MaxRepliesPerUser = 0;
        TotalRepliesSent = 0;
        PublicRepliesSent = 0;
        PrivateRepliesSent = 0;
    }

    public void UpdateMessages(string publicReplyMessage, string? privateReplyMessage = null)
    {
        PublicReplyMessage = publicReplyMessage;
        if (!string.IsNullOrEmpty(privateReplyMessage))
        {
            PrivateReplyMessage = privateReplyMessage;
        }
    }

    public void ConfigureReplyTypes(bool sendPublicReply, bool sendPrivateReply, bool sendLike = false)
    {
        SendPublicReply = sendPublicReply;
        SendPrivateReply = sendPrivateReply;
        SendLike = sendLike;
    }

    public void SetEndDate(DateTime? endDate)
    {
        EndDate = endDate;
    }

    public void SetMaxRepliesPerUser(int maxReplies)
    {
        if (maxReplies >= 0)
        {
            MaxRepliesPerUser = maxReplies;
        }
    }

    public void IncrementReplyCount(bool isPublicReply)
    {
        TotalRepliesSent++;
        if (isPublicReply)
        {
            PublicRepliesSent++;
        }
        else
        {
            PrivateRepliesSent++;
        }
        LastReplyAt = DateTime.UtcNow;
    }

    public void IncrementLikeCount()
    {
        TotalRepliesSent++;
        LikesSent++;
        LastReplyAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public bool IsValidForReply()
    {
        if (!IsActive) return false;

        var now = DateTime.UtcNow;

        if (EndDate.HasValue && now > EndDate.Value.ToUniversalTime()) return false;

        return true;
    }

    public void UpdatePostMetadata(string? permalinkUrl, string? pictureUrl)
    {
        PostPermalinkUrl = permalinkUrl;
        PostPictureUrl = pictureUrl;
    }

    /// <summary>
    /// Checks if this campaign allows unlimited replies per user
    /// </summary>
    /// <returns>True if MaxRepliesPerUser is 0 (unlimited), false otherwise</returns>
    public bool AllowsUnlimitedReplies()
    {
        return MaxRepliesPerUser == 0;
    }

    /// <summary>
    /// Checks if a user has exceeded the maximum replies allowed for this campaign
    /// </summary>
    /// <param name="userReplyCount">The number of replies the user has already received</param>
    /// <returns>True if the user has exceeded the limit, false otherwise</returns>
    public bool HasUserExceededReplyLimit(int userReplyCount)
    {
        if (AllowsUnlimitedReplies())
        {
            return false; // Unlimited replies - never exceeded
        }

        return userReplyCount >= MaxRepliesPerUser;
    }

    /// <summary>
    /// Gets the card reply data from JSON
    /// </summary>
    /// <returns>CardReplyData object or null if not set or invalid</returns>
    public CardReplyData? GetCardReplyData()
    {
        if (string.IsNullOrWhiteSpace(CardReplyDataJson))
            return null;

        try
        {
            return JsonSerializer.Deserialize<CardReplyData>(CardReplyDataJson);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Sets the card reply data as JSON
    /// </summary>
    /// <param name="cardReplyData">The card reply data to set</param>
    public void SetCardReplyData(CardReplyData? cardReplyData)
    {
        if (cardReplyData == null)
        {
            CardReplyDataJson = null;
        }
        else
        {
            CardReplyDataJson = JsonSerializer.Serialize(cardReplyData);
        }
    }

    /// <summary>
    /// Validates the private reply configuration
    /// </summary>
    /// <returns>True if the configuration is valid</returns>
    public bool IsPrivateReplyConfigurationValid()
    {
        if (!SendPrivateReply)
            return true; // No private reply configured, so it's valid

        return PrivateReplyType switch
        {
            PrivateReplyType.TextReply => !string.IsNullOrWhiteSpace(PrivateReplyMessage),
            PrivateReplyType.CardReply => GetCardReplyData()?.IsValid() == true,
            _ => false
        };
    }
}
