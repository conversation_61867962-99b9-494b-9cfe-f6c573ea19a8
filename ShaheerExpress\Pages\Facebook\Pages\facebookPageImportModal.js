const FacebookPageImportModal = {
    emits: ['pages-imported'],
    
    data() {
        return {
            isOpen: false,
            loading: false,
            importing: false,
            availablePages: [],
            selectedPages: [],
            l: null
        };
    },
    
    computed: {
        hasAvailablePages() {
            return this.availablePages && this.availablePages.length > 0;
        },
        
        allSelected() {
            return this.availablePages.length > 0 && this.selectedPages.length === this.availablePages.length;
        },
        
        someSelected() {
            return this.selectedPages.length > 0 && this.selectedPages.length < this.availablePages.length;
        }
    },
    
    template: `
        <div class="modal fade" :class="{ show: isOpen }" :style="{ display: isOpen ? 'block' : 'none' }" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-download me-2"></i>
                            {{ l('Facebook:ImportPages') || 'Import Facebook Pages' }}
                        </h5>
                        <button type="button" class="btn-close" v-on:click="close"></button>
                    </div>
                    
                    <div class="modal-body">
                        <!-- Loading State -->
                        <div v-if="loading" class="text-center py-4">
                            <div class="loading-spinner"></div>
                            <p class="mt-2 text-muted">{{ l('Facebook:LoadingAvailablePages') || 'Loading available pages from Facebook...' }}</p>
                        </div>
                        
                        <!-- No Pages Available -->
                        <div v-else-if="!hasAvailablePages" class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ l('Facebook:NoAvailablePages') || 'No pages available for import. Make sure you have admin access to Facebook pages.' }}
                        </div>
                        
                        <!-- Available Pages -->
                        <div v-else>
                            <div class="mb-3">
                                <p>{{ l('Facebook:SelectPagesToImport') || 'Select the pages you want to import:' }}</p>
                                
                                <!-- Select All Checkbox -->
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="selectAll"
                                           :checked="allSelected"
                                           :indeterminate="someSelected"
                                           v-on:change="toggleSelectAll">
                                    <label class="form-check-label fw-bold" for="selectAll">
                                        {{ l('Facebook:SelectAll') || 'Select All' }} ({{ availablePages.length }})
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Page List -->
                            <div class="available-pages-list" style="max-height: 400px; overflow-y: auto;">
                                <div v-for="page in availablePages" 
                                     :key="page.facebookPageId" 
                                     class="import-page-item"
                                     :class="{ selected: isPageSelected(page.facebookPageId) }">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               :id="'page_' + page.facebookPageId"
                                               :value="page.facebookPageId"
                                               v-model="selectedPages">
                                        <label class="form-check-label w-100" :for="'page_' + page.facebookPageId">
                                            <div class="d-flex align-items-center">
                                                <!-- Page Avatar -->
                                                <div class="me-3">
                                                    <img v-if="page.pageProfilePictureUrl" 
                                                         :src="page.pageProfilePictureUrl" 
                                                         :alt="page.pageName"
                                                         class="page-avatar">
                                                    <i v-else class="fas fa-file-alt text-muted" style="font-size: 32px;"></i>
                                                </div>
                                                
                                                <!-- Page Info -->
                                                <div class="flex-grow-1">
                                                    <div class="fw-bold">{{ page.pageName }}</div>
                                                    <div class="text-muted small">
                                                        <span v-if="page.category">{{ page.category }} • </span>
                                                        {{ formatNumber(page.followersCount) }} {{ l('Facebook:Followers') || 'followers' }}
                                                    </div>
                                                </div>
                                                
                                                <!-- Selection Indicator -->
                                                <div v-if="isPageSelected(page.facebookPageId)" class="text-primary">
                                                    <i class="fas fa-check-circle"></i>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" v-on:click="close" :disabled="importing">
                            {{ l('Cancel') || 'Cancel' }}
                        </button>
                        <button type="button" 
                                class="btn btn-primary" 
                                v-on:click="importSelectedPages" 
                                :disabled="selectedPages.length === 0 || importing">
                            <div v-if="importing" class="loading-spinner me-2"></div>
                            <i v-else class="fas fa-download me-2"></i>
                            {{ importing ? (l('Facebook:ImportingPages') || 'Importing...') : (l('Facebook:ImportSelected') || 'Import Selected') }}
                            <span v-if="!importing && selectedPages.length > 0" class="badge bg-light text-dark ms-2">
                                {{ selectedPages.length }}
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Modal Backdrop -->
        <div v-if="isOpen" class="modal-backdrop fade show"></div>
    `,
    
    methods: {
        async open() {
            this.isOpen = true;
            this.selectedPages = [];
            await this.loadAvailablePages();
        },
        
        close() {
            this.isOpen = false;
            this.availablePages = [];
            this.selectedPages = [];
        },
        
        async loadAvailablePages() {
            try {
                this.loading = true;
                this.availablePages = await window.shaheerExpress.services.facebookPage.getAvailablePagesFromFacebook();
            } catch (error) {
                console.error('Error loading available pages:', error);
                abp.notify.error(this.l('Facebook:ErrorLoadingAvailablePages') || 'Error loading available pages from Facebook');
                this.availablePages = [];
            } finally {
                this.loading = false;
            }
        },
        
        async importSelectedPages() {
            if (this.selectedPages.length === 0) return;
            
            try {
                this.importing = true;
                
                // Get the selected page objects
                const pagesToImport = this.availablePages.filter(page => 
                    this.selectedPages.includes(page.facebookPageId)
                );
                
                // Import pages
                await window.shaheerExpress.services.facebookPage.importMultiplePages(pagesToImport);
                
                abp.notify.success(
                    this.l('Facebook:PagesImportedSuccess', this.selectedPages.length) || 
                    `${this.selectedPages.length} pages imported successfully`
                );
                
                // Emit event to parent component
                this.$emit('pages-imported');
                
                // Close modal
                this.close();
            } catch (error) {
                console.error('Error importing pages:', error);
                abp.notify.error(this.l('Facebook:ErrorImportingPages') || 'Error importing pages');
            } finally {
                this.importing = false;
            }
        },
        
        toggleSelectAll(event) {
            if (event.target.checked) {
                this.selectedPages = this.availablePages.map(page => page.facebookPageId);
            } else {
                this.selectedPages = [];
            }
        },
        
        isPageSelected(pageId) {
            return this.selectedPages.includes(pageId);
        },
        
        formatNumber(num) {
            if (!num) return '0';
            return num.toLocaleString();
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },

    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
        
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
};
