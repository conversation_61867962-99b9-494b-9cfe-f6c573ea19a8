using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using ShaheerExpress.Entities;
using ShaheerExpress.Permissions;
using ShaheerExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace ShaheerExpress.Services;

[Authorize]
public class FacebookPostPublishingService : ApplicationService, IFacebookPostPublishingService
{
    private readonly IRepository<ScheduledFacebookPost, Guid> _scheduledPostRepository;
    private readonly IRepository<FacebookPage, Guid> _facebookPageRepository;
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly ILogger<FacebookPostPublishingService> _logger;

    public FacebookPostPublishingService(
        IRepository<ScheduledFacebookPost, Guid> scheduledPostRepository,
        IRepository<FacebookPage, Guid> facebookPageRepository,
        IRepository<FacebookUser, Guid> facebookUserRepository,
        FacebookGraphApiService facebookGraphApiService,
        ILogger<FacebookPostPublishingService> logger)
    {
        _scheduledPostRepository = scheduledPostRepository;
        _facebookPageRepository = facebookPageRepository;
        _facebookUserRepository = facebookUserRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _logger = logger;
    }

    [Authorize(ShaheerExpressPermissions.Posts.Create)]
    public async Task<FacebookPostPublishResultDto> PublishPostAsync(PublishFacebookPostDto input)
    {
        try
        {
            // Validate input
            if (!input.IsValid)
            {
                throw new UserFriendlyException("Post must contain either message, media, or link content.");
            }

            // Get and validate page access
            var page = await GetAndValidatePageAccessAsync(input.FacebookPageId);

            // Handle media uploads first
            var mediaIds = new List<string>();
            if (input.Media.Any())
            {
                foreach (var media in input.Media)
                {
                    var mediaId = await _facebookGraphApiService.UploadMediaAsync(
                        page.FacebookPageId, page.PageAccessToken, media.Content, media.ContentType, media.FileName);
                    mediaIds.Add(mediaId);
                }
            }

            // Create Facebook API request
            var publishRequest = new FacebookPostPublishRequest
            {
                Message = input.Message,
                MediaIds = mediaIds,
                Link = input.LinkUrl,
                Published = input.PublishNow,
                ScheduledPublishTime = input.ScheduledPublishTime.HasValue
                    ? ((DateTimeOffset)input.ScheduledPublishTime.Value).ToUnixTimeSeconds()
                    : null
            };

            if (input.PublishNow)
            {
                // Publish immediately
                var facebookPostId = await _facebookGraphApiService.PublishPostAsync(
                    page.FacebookPageId, page.PageAccessToken, publishRequest);

                return new FacebookPostPublishResultDto
                {
                    Success = true,
                    FacebookPostId = facebookPostId,
                    Status = PostScheduleStatus.Published,
                    PublishedAt = DateTime.UtcNow
                };
            }
            else
            {
                // Schedule for later
                if (!input.ScheduledPublishTime.HasValue)
                {
                    throw new UserFriendlyException("Scheduled publish time is required when not publishing immediately.");
                }

                // Create scheduled post entity
                var scheduledPost = new ScheduledFacebookPost(
                    GuidGenerator.Create(),
                    input.FacebookPageId,
                    input.PostType,
                    input.Message,
                    mediaIds.Any() ? mediaIds : null,
                    input.LinkUrl,
                    input.ScheduledPublishTime.Value);

                await _scheduledPostRepository.InsertAsync(scheduledPost);

                // Use Facebook's native scheduling
                var facebookPostId = await _facebookGraphApiService.PublishPostAsync(
                    page.FacebookPageId, page.PageAccessToken, publishRequest);

                scheduledPost.MarkAsPublished(facebookPostId, input.ScheduledPublishTime.Value);
                await _scheduledPostRepository.UpdateAsync(scheduledPost);

                return new FacebookPostPublishResultDto
                {
                    Success = true,
                    FacebookPostId = facebookPostId,
                    Status = PostScheduleStatus.Scheduled,
                    ScheduledFor = input.ScheduledPublishTime.Value
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing Facebook post for page {PageId}", input.FacebookPageId);
            
            return new FacebookPostPublishResultDto
            {
                Success = false,
                ErrorMessage = ex.Message,
                ErrorType = DetermineErrorType(ex),
                Status = PostScheduleStatus.Failed
            };
        }
    }

    public async Task<FacebookPostPreviewDto> GeneratePostPreviewAsync(PublishFacebookPostDto input)
    {
        var page = await GetAndValidatePageAccessAsync(input.FacebookPageId);

        var preview = new FacebookPostPreviewDto
        {
            Message = input.Message,
            LinkUrl = input.LinkUrl,
            PostType = input.PostType,
            PageName = page.PageName,
            PageProfilePictureUrl = page.PageProfilePictureUrl
        };

        // Generate media URLs for preview (in a real implementation, you might want to create temporary URLs)
        if (input.Media.Any())
        {
            preview.MediaUrls = input.Media.Select(m => $"data:{m.ContentType};base64,{Convert.ToBase64String(m.Content)}").ToList();
        }

        // Get link preview if URL is provided
        if (!string.IsNullOrEmpty(input.LinkUrl))
        {
            try
            {
                var facebookUser = await GetCurrentUserFacebookUserAsync();
                var linkPreview = await _facebookGraphApiService.GetLinkPreviewAsync(input.LinkUrl, facebookUser.AccessToken);
                preview.LinkTitle = linkPreview.Title;
                preview.LinkDescription = linkPreview.Description;
                preview.LinkImageUrl = linkPreview.ImageUrl;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get link preview for URL: {Url}", input.LinkUrl);
            }
        }

        return preview;
    }

    public async Task<FacebookLinkPreviewDto> GetLinkPreviewAsync(string url)
    {
        try
        {
            var facebookUser = await GetCurrentUserFacebookUserAsync();
            var linkPreview = await _facebookGraphApiService.GetLinkPreviewAsync(url, facebookUser.AccessToken);
            
            return new FacebookLinkPreviewDto
            {
                Url = url,
                Title = linkPreview.Title,
                Description = linkPreview.Description,
                ImageUrl = linkPreview.ImageUrl,
                IsValid = linkPreview.IsValid
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get link preview for URL: {Url}", url);
            return new FacebookLinkPreviewDto
            {
                Url = url,
                IsValid = false
            };
        }
    }

    private async Task<FacebookPage> GetAndValidatePageAccessAsync(Guid facebookPageId)
    {
        var currentUserId = CurrentUser.Id;
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);
        
        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected. Please connect your Facebook account first.");
        }

        var page = await _facebookPageRepository.FirstOrDefaultAsync(x => x.Id == facebookPageId && x.FacebookUserId == facebookUser.Id);
        
        if (page == null)
        {
            throw new UserFriendlyException("Facebook page not found or you don't have access to it.");
        }

        if (!page.IsConnected)
        {
            throw new UserFriendlyException("Facebook page is disconnected. Please reconnect the page.");
        }

        return page;
    }

    private async Task<FacebookUser> GetCurrentUserFacebookUserAsync()
    {
        var currentUserId = CurrentUser.Id;
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);
        
        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected.");
        }

        return facebookUser;
    }

    private static FacebookErrorType DetermineErrorType(Exception ex)
    {
        var message = ex.Message.ToLower();
        
        if (message.Contains("token") && (message.Contains("expired") || message.Contains("invalid")))
            return FacebookErrorType.TokenExpired;
        
        if (message.Contains("permission"))
            return FacebookErrorType.MissingPermissions;
        
        if (message.Contains("rate") && message.Contains("limit"))
            return FacebookErrorType.RateLimited;
        
        return FacebookErrorType.General;
    }

    [Authorize(ShaheerExpressPermissions.Posts.Default)]
    public async Task<PagedResultDto<ScheduledFacebookPostDto>> GetScheduledPostsAsync(GetScheduledPostsInput input)
    {
        var currentUserId = CurrentUser.Id;
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var query = await _scheduledPostRepository.GetQueryableAsync();

        // Join with FacebookPage to filter by user's pages
        var pageQuery = await _facebookPageRepository.GetQueryableAsync();
        var userPages = pageQuery.Where(p => p.FacebookUserId == facebookUser.Id).Select(p => p.Id);

        query = query.Where(sp => userPages.Contains(sp.FacebookPageId));

        // Apply filters
        if (input.FacebookPageId.HasValue)
        {
            query = query.Where(sp => sp.FacebookPageId == input.FacebookPageId.Value);
        }

        if (input.Status.HasValue)
        {
            query = query.Where(sp => sp.Status == input.Status.Value);
        }

        if (input.FromDate.HasValue)
        {
            query = query.Where(sp => sp.ScheduledPublishTime >= input.FromDate.Value);
        }

        if (input.ToDate.HasValue)
        {
            query = query.Where(sp => sp.ScheduledPublishTime <= input.ToDate.Value);
        }

        // Apply sorting
        if (!string.IsNullOrEmpty(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(sp => sp.ScheduledPublishTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(query.Skip(input.SkipCount).Take(input.MaxResultCount));

        var dtos = new List<ScheduledFacebookPostDto>();
        foreach (var item in items)
        {
            var page = await _facebookPageRepository.GetAsync(item.FacebookPageId);
            var dto = ObjectMapper.Map<ScheduledFacebookPost, ScheduledFacebookPostDto>(item);
            dto.PageName = page.PageName;
            dto.PageProfilePictureUrl = page.PageProfilePictureUrl;
            dtos.Add(dto);
        }

        return new PagedResultDto<ScheduledFacebookPostDto>(totalCount, dtos);
    }

    [Authorize(ShaheerExpressPermissions.Posts.Default)]
    public async Task<ScheduledFacebookPostDto> GetScheduledPostAsync(Guid id)
    {
        var scheduledPost = await _scheduledPostRepository.GetAsync(id);
        await ValidateScheduledPostAccessAsync(scheduledPost);

        var page = await _facebookPageRepository.GetAsync(scheduledPost.FacebookPageId);
        var dto = ObjectMapper.Map<ScheduledFacebookPost, ScheduledFacebookPostDto>(scheduledPost);
        dto.PageName = page.PageName;
        dto.PageProfilePictureUrl = page.PageProfilePictureUrl;

        return dto;
    }

    [Authorize(ShaheerExpressPermissions.Posts.Edit)]
    public async Task<ScheduledFacebookPostDto> UpdateScheduledPostAsync(Guid id, UpdateScheduledPostDto input)
    {
        var scheduledPost = await _scheduledPostRepository.GetAsync(id);
        await ValidateScheduledPostAccessAsync(scheduledPost);

        if (!scheduledPost.CanBeModified)
        {
            throw new UserFriendlyException("This scheduled post cannot be modified.");
        }

        if (!string.IsNullOrEmpty(input.Message))
        {
            scheduledPost.UpdateContent(input.Message, scheduledPost.MediaUrls, scheduledPost.LinkUrl);
        }

        if (input.ScheduledPublishTime.HasValue)
        {
            scheduledPost.UpdateSchedule(input.ScheduledPublishTime.Value);
        }

        if (input.Status.HasValue)
        {
            switch (input.Status.Value)
            {
                case PostScheduleStatus.Cancelled:
                    scheduledPost.MarkAsCancelled();
                    break;
            }
        }

        await _scheduledPostRepository.UpdateAsync(scheduledPost);

        var page = await _facebookPageRepository.GetAsync(scheduledPost.FacebookPageId);
        var dto = ObjectMapper.Map<ScheduledFacebookPost, ScheduledFacebookPostDto>(scheduledPost);
        dto.PageName = page.PageName;
        dto.PageProfilePictureUrl = page.PageProfilePictureUrl;

        return dto;
    }

    [Authorize(ShaheerExpressPermissions.Posts.Delete)]
    public async Task CancelScheduledPostAsync(Guid id)
    {
        var scheduledPost = await _scheduledPostRepository.GetAsync(id);
        await ValidateScheduledPostAccessAsync(scheduledPost);

        if (!scheduledPost.CanBeModified)
        {
            throw new UserFriendlyException("This scheduled post cannot be cancelled.");
        }

        scheduledPost.MarkAsCancelled();
        await _scheduledPostRepository.UpdateAsync(scheduledPost);
    }

    [Authorize(ShaheerExpressPermissions.Posts.Create)]
    public async Task<FacebookPostPublishResultDto> PublishScheduledPostNowAsync(Guid id)
    {
        var scheduledPost = await _scheduledPostRepository.GetAsync(id);
        await ValidateScheduledPostAccessAsync(scheduledPost);

        if (scheduledPost.Status != PostScheduleStatus.Scheduled)
        {
            throw new UserFriendlyException("Only scheduled posts can be published immediately.");
        }

        try
        {
            var page = await _facebookPageRepository.GetAsync(scheduledPost.FacebookPageId);

            var publishRequest = new FacebookPostPublishRequest
            {
                Message = scheduledPost.Message,
                MediaIds = scheduledPost.MediaUrls,
                Link = scheduledPost.LinkUrl,
                Published = true
            };

            var facebookPostId = await _facebookGraphApiService.PublishPostAsync(
                page.FacebookPageId, page.PageAccessToken, publishRequest);

            scheduledPost.MarkAsPublished(facebookPostId, DateTime.UtcNow);
            await _scheduledPostRepository.UpdateAsync(scheduledPost);

            return new FacebookPostPublishResultDto
            {
                Success = true,
                FacebookPostId = facebookPostId,
                Status = PostScheduleStatus.Published,
                PublishedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing scheduled post {PostId}", id);

            scheduledPost.MarkAsFailed(ex.Message);
            await _scheduledPostRepository.UpdateAsync(scheduledPost);

            return new FacebookPostPublishResultDto
            {
                Success = false,
                ErrorMessage = ex.Message,
                ErrorType = DetermineErrorType(ex),
                Status = PostScheduleStatus.Failed
            };
        }
    }

    public async Task<bool> CanPublishToPageAsync(Guid facebookPageId)
    {
        try
        {
            await GetAndValidatePageAccessAsync(facebookPageId);
            return true;
        }
        catch
        {
            return false;
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ViewPages)]
    public async Task<List<FacebookPageDto>> GetAvailablePagesForPostingAsync()
    {
        var currentUserId = CurrentUser.Id;
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);

        if (facebookUser == null)
        {
            return new List<FacebookPageDto>();
        }

        var pages = await _facebookPageRepository.GetListAsync(x => x.FacebookUserId == facebookUser.Id && x.IsConnected);
        return ObjectMapper.Map<List<FacebookPage>, List<FacebookPageDto>>(pages);
    }

    private async Task ValidateScheduledPostAccessAsync(ScheduledFacebookPost scheduledPost)
    {
        var currentUserId = CurrentUser.Id;
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);

        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected.");
        }

        var page = await _facebookPageRepository.FirstOrDefaultAsync(x => x.Id == scheduledPost.FacebookPageId && x.FacebookUserId == facebookUser.Id);

        if (page == null)
        {
            throw new UserFriendlyException("You don't have access to this scheduled post.");
        }
    }
}
