@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@inject IJSRuntime JSRuntime

<Card>
    <CardHeader>
        <h5 class="mb-0">
            <Icon Name="IconName.Edit" class="me-2" />
            Create Post
        </h5>
    </CardHeader>
    <CardBody>
        <!-- Post Type Selection -->
        <Field>
            <FieldLabel>Post Type</FieldLabel>
            <Select TValue="FacebookPostType" @bind-Value="PostData.PostType" @onchange="OnPostTypeChanged">
                <SelectItem Value="FacebookPostType.Text">Text Post</SelectItem>
                <SelectItem Value="FacebookPostType.Image">Image Post</SelectItem>
                <SelectItem Value="FacebookPostType.Video">Video Post</SelectItem>
                <SelectItem Value="FacebookPostType.Link">Link Post</SelectItem>
            </Select>
        </Field>

        <!-- Message Text Area with Emoji Picker -->
        <Field>
            <FieldLabel>Message</FieldLabel>
            <div class="position-relative">
                <MemoEdit @ref="messageTextArea"
                         Text="@PostData.Message"
                         Placeholder="What's on your mind?"
                         Rows="4"
                         MaxLength="2048"
                          @oninput="(e) => OnMessageChanged(e)"
                          @onchange="(e) => OnMessageChanged(e)" />
                
                <!-- Emoji Picker positioned at top-right of text area -->
                <div class="emoji-picker-wrapper">
                    <EmojiPicker OnEmojiSelected="OnEmojiSelected" Alignment="EmojiPicker.PickerAlignment.Right" />
                </div>
            </div>
            <FieldHelp>@($"{(PostData.Message?.Length ?? 0)}/2048 characters")</FieldHelp>
        </Field>

        <!-- Media Upload Area (for Image/Video posts) -->
        @if (PostData.PostType == FacebookPostType.Image || PostData.PostType == FacebookPostType.Video)
        {
            <Field>
                <FieldLabel>@(PostData.PostType == FacebookPostType.Image ? "Images" : "Video")</FieldLabel>
                <MediaUploadArea @bind-Media="PostData.Media" 
                               PostType="PostData.PostType"
                               OnMediaChanged="OnMediaChanged" />
            </Field>
        }

        <!-- Link Input (for Link posts) -->
        @if (PostData.PostType == FacebookPostType.Link)
        {
            <Field>
                <FieldLabel>Link URL</FieldLabel>
                <TextEdit @bind-Text="PostData.LinkUrl" 
                         Placeholder="https://example.com"
                         @onblur="OnLinkUrlChanged" />
                <FieldHelp>Enter a URL to create a link preview</FieldHelp>
            </Field>
        }

        <!-- Character Counter and Validation -->
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted small">
                @if (!PostData.IsValid)
                {
                    <span class="text-danger">
                        <Icon Name="IconName.ExclamationTriangle" class="me-1" />
                        Post must contain message, media, or link content
                    </span>
                }
                else
                {
                    <span class="text-success">
                        <Icon Name="IconName.CheckCircle" class="me-1" />
                        Ready to post
                    </span>
                }
            </div>
            
            <div class="text-muted small">
                @if (PostData.Media.Any())
                {
                    <span>@PostData.Media.Count @(PostData.Media.Count == 1 ? "file" : "files") attached</span>
                }
            </div>
        </div>
    </CardBody>
</Card>

@code {
    [Parameter] public PublishFacebookPostDto PostData { get; set; } = new();
    [Parameter] public EventCallback<PublishFacebookPostDto> PostDataChanged { get; set; }
    [Parameter] public EventCallback OnContentChanged { get; set; }

    private MemoEdit? messageTextArea;

    protected override async Task OnInitializedAsync()
    {
        // Initialize with default values if needed
        if (PostData.PostType == default)
        {
            PostData.PostType = FacebookPostType.Text;
        }
    }

    private async Task OnPostTypeChanged(ChangeEventArgs e)
    {
        if (Enum.TryParse<FacebookPostType>(e.Value?.ToString(), out var postType))
        {
            PostData.PostType = postType;
            
            // Clear media when switching away from media post types
            if (postType != FacebookPostType.Image && postType != FacebookPostType.Video)
            {
                PostData.Media.Clear();
            }
            
            // Clear link when switching away from link post type
            if (postType != FacebookPostType.Link)
            {
                PostData.LinkUrl = null;
            }

            await NotifyContentChanged();
        }
    }

    private async Task OnMessageChanged(ChangeEventArgs e)
    {
        PostData.Message = e.Value?.ToString();
        await NotifyContentChanged();
    }

    private async Task OnEmojiSelected(string emoji)
    {
        if (messageTextArea != null)
        {
            // Get current cursor position and insert emoji
            var currentText = PostData.Message ?? "";
            var cursorPosition = await JSRuntime.InvokeAsync<int>("getCursorPosition", messageTextArea.ElementRef);
            
            var newText = currentText.Insert(Math.Min(cursorPosition, currentText.Length), emoji);
            PostData.Message = newText;
            
            await NotifyContentChanged();
            
            // Set cursor position after emoji
            await JSRuntime.InvokeVoidAsync("setCursorPosition", messageTextArea.ElementRef, cursorPosition + emoji.Length);
        }
        else
        {
            PostData.Message = (PostData.Message ?? "") + emoji;
            await NotifyContentChanged();
        }
    }

    private async Task OnMediaChanged()
    {
        await NotifyContentChanged();
    }

    private async Task OnLinkUrlChanged()
    {
        await NotifyContentChanged();
    }

    private async Task NotifyContentChanged()
    {
        await PostDataChanged.InvokeAsync(PostData);
        await OnContentChanged.InvokeAsync();
    }
}

<style>
    .emoji-picker-wrapper {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 10;
    }

    .position-relative {
        position: relative;
    }
</style>

<!-- JavaScript functions moved to interop.js -->
