namespace ShaheerExpress.Services.Dtos;

public enum FacebookErrorType
{
    General,
    TokenExpired,
    TokenInvalid,
    MissingPermissions,
    ConnectionLost,
    RateLimited
}

public enum PrivateReplyType
{
    TextReply = 0,
    CardReply = 1
}

public enum PublicReplyType
{
    Custom = 0,
    Emoji = 1,
    Welcome = 2
}

public enum FacebookPostType
{
    Text = 0,
    Image = 1,
    Video = 2,
    Link = 3
}

public enum PostScheduleStatus
{
    Draft = 0,
    Scheduled = 1,
    Published = 2,
    Failed = 3,
    Cancelled = 4
}
