﻿using ShaheerExpress.Entities.Authors;
using Volo.Abp.Domain.Repositories;
using YamlDotNet.Core.Tokens;

namespace ShaheerExpress.Data.Authors
{
    public interface IAuthorRepository : IRepository<Author, Guid>
    {
        Task<List<Author>> GetListAsync(
            string? filterText = null,
            string? name = null,
            DateOnly? birthdateMin = null,
            DateOnly? birthdateMax = null,
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string? filterText = null,
            string? name = null,
            DateOnly? birthdateMin = null,
            DateOnly? birthdateMax = null,
            CancellationToken cancellationToken = default);
    }
}
