function initializeVueApp() {
    if (typeof Vue === 'undefined') {
        setTimeout(initializeVueApp, 100);
        return;
    }
    else
        createVueApp();
}

function createVueApp() {
    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                pages: [],
                totalCount: 0,
                currentPage: 1,
                pageSize: 10,
                loading: false,
                pagesLoading: false,
                loadingMessage: 'Loading...',
                isConnected: false,
                isValidatingTokens: false,
                pageTokenStatuses: {},
                l: null,
                
                // Permissions
                canManagePages: false,
                canViewPages: false
            };
        },
        
        computed: {
            totalPagesCount() {
                return Object.keys(this.pageTokenStatuses).length;
            },
            
            validPagesCount() {
                return Object.values(this.pageTokenStatuses).filter(status => 
                    status.isTokenValid && !status.requiresReconnection
                ).length;
            },
            
            tokenIssueCount() {
                return Object.values(this.pageTokenStatuses).filter(status => 
                    status.requiresReconnection
                ).length;
            }
        },
        
        methods: {
            async loadInitialData() {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Loading Facebook connection status...';
                    
                    // Check if user is connected to Facebook
                    this.isConnected = await window.shaheerExpress.services.facebookAuth.isConnectedToFacebook();
                    
                    if (this.isConnected) {
                        await this.loadPages();
                        await this.validateAllPageTokens();
                    }
                } catch (error) {
                    console.error('Error loading initial data:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingData') || 'Error loading data');
                } finally {
                    this.loading = false;
                }
            },
            
            async loadPages() {
                try {
                    this.pagesLoading = true;
                    
                    const params = {
                        skipCount: (this.currentPage - 1) * this.pageSize,
                        maxResultCount: this.pageSize,
                        sorting: 'creationTime desc'
                    };
                    
                    const response = await window.shaheerExpress.services.facebookPage.getList(params);
                    this.pages = response.items;
                    this.totalCount = response.totalCount;
                } catch (error) {
                    console.error('Error loading pages:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingPages') || 'Error loading Facebook pages');
                } finally {
                    this.pagesLoading = false;
                }
            },
            
            async validateAllPageTokens() {
                
                if (!this.isConnected || this.pages.length === 0) {
                    console.log('not connected or no pages!')
                    return
                };
                
                try {
                    this.isValidatingTokens = true;
                    
                    // Validate tokens for all pages
                    for (const page of this.pages) {
                        try {
                            // This would need to be implemented in the backend
                            // For now, we'll simulate token validation
                            const validation = await window.shaheerExpress.services.facebookAuth.validatePageToken(page.id);

                            this.pageTokenStatuses[page.id] = {
                                isTokenValid: validation.isTokenValid,
                                isTokenExpired: validation.isTokenExpired,
                                requiresReconnection: validation.requiresReconnection,
                                errorMessage: validation.rrrorMessage
                            };
                        } catch (error) {
                            console.error(`Error validating token for page ${page.id}:`, error);
                            this.pageTokenStatuses[page.id] = {
                                isTokenValid: false,
                                isTokenExpired: true,
                                requiresReconnection: true,
                                errorMessage: 'Token validation failed'
                            };
                        }
                    }
                } catch (error) {
                    console.error('Error validating page tokens:', error);
                    abp.notify.error(this.l('Facebook:ErrorValidatingTokens') || 'Error validating page tokens');
                } finally {
                    this.isValidatingTokens = false;
                }
            },
            
            async syncAllPages() {
                if (!this.canManagePages) return;
                
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:SyncAllConfirmation') || 'Are you sure you want to sync all pages? This may take a few minutes.',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Syncing all pages...';
                        
                        await window.shaheerExpress.services.facebookPage.syncAllPages();
                        abp.notify.success(this.l('Facebook:SyncAllSuccess') || 'All pages synced successfully');
                        
                        // Reload pages
                        await this.loadPages();
                    } catch (error) {
                        console.error('Error syncing all pages:', error);
                        abp.notify.error(this.l('Facebook:ErrorSyncingPages') || 'Error syncing pages');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            openImportModal() {
                if (this.$refs.importModalComponent) {
                    this.$refs.importModalComponent.open();
                }
            },
            
            navigateToConnection() {
                window.location.href = '/Facebook/Connection';
            },
            
            handlePageChanged(page) {
                this.currentPage = page;
                this.loadPages();
            },
            
            async handleSyncPage(pageId) {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Syncing page...';
                    
                    await window.shaheerExpress.services.facebookPage.syncPageInfo(pageId);
                    abp.notify.success(this.l('Facebook:PageSyncSuccess') || 'Page synced successfully');
                    
                    // Reload pages
                    await this.loadPages();
                } catch (error) {
                    console.error('Error syncing page:', error);
                    abp.notify.error(this.l('Facebook:ErrorSyncingPage') || 'Error syncing page');
                } finally {
                    this.loading = false;
                }
            },
            
            async handleDisconnectPage(pageId) {
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:DisconnectPageConfirmation') || 'Are you sure you want to disconnect this page?',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Disconnecting page...';
                        
                        await window.shaheerExpress.services.facebookPage.disconnectPage(pageId);
                        abp.notify.success(this.l('Facebook:PageDisconnectedSuccess') || 'Page disconnected successfully');
                        
                        // Reload pages
                        await this.loadPages();
                    } catch (error) {
                        console.error('Error disconnecting page:', error);
                        abp.notify.error(this.l('Facebook:ErrorDisconnectingPage') || 'Error disconnecting page');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            async handleReconnectPage(pageId) {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Reconnecting page...';
                    
                    await window.shaheerExpress.services.facebookPage.reconnectPage(pageId);
                    abp.notify.success(this.l('Facebook:PageReconnectedSuccess') || 'Page reconnected successfully');
                    
                    // Reload pages
                    await this.loadPages();
                } catch (error) {
                    console.error('Error reconnecting page:', error);
                    abp.notify.error(this.l('Facebook:ErrorReconnectingPage') || 'Error reconnecting page');
                } finally {
                    this.loading = false;
                }
            },
            
            async handleSubscribeWebhook(pageId) {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Subscribing to webhook...';
                    
                    await window.shaheerExpress.services.facebookPage.subscribeToWebhook(pageId);
                    abp.notify.success(this.l('Facebook:WebhookSubscribedSuccess') || 'Webhook subscribed successfully');
                    
                    // Reload pages
                    await this.loadPages();
                } catch (error) {
                    console.error('Error subscribing to webhook:', error);
                    abp.notify.error(this.l('Facebook:ErrorSubscribingWebhook') || 'Error subscribing to webhook');
                } finally {
                    this.loading = false;
                }
            },
            
            async handleUnsubscribeWebhook(pageId) {
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:UnsubscribeWebhookConfirmation') || 'Are you sure you want to disable webhook for this page?',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Unsubscribing from webhook...';
                        
                        await window.shaheerExpress.services.facebookPage.unsubscribeFromWebhook(pageId);
                        abp.notify.success(this.l('Facebook:WebhookUnsubscribedSuccess') || 'Webhook unsubscribed successfully');
                        
                        // Reload pages
                        await this.loadPages();
                    } catch (error) {
                        console.error('Error unsubscribing from webhook:', error);
                        abp.notify.error(this.l('Facebook:ErrorUnsubscribingWebhook') || 'Error unsubscribing from webhook');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            handleCreateCampaign(pageId) {
                // Navigate to campaign creation page with page ID
                window.location.href = `/Facebook/Campaigns/Create?pageId=${pageId}`;
            },
            
            async handlePagesImported() {
                // Reload pages after import
                await this.loadPages();
                await this.validateAllPageTokens();
            }
        },
        
        async mounted() {
            // Initialize ABP localization
            this.l = abp.localization.getResource('ShaheerExpress');
            
            // Check permissions
            this.canViewPages = abp.auth.isGranted('ShaheerExpress.Facebook.ViewPages');
            this.canManagePages = abp.auth.isGranted('ShaheerExpress.Facebook.ManagePages');
            
            // Load initial data
            await this.loadInitialData();
        }
    });
    
    // Register components (will be defined in separate files)
    app.component('facebook-page-list', FacebookPageList);
    app.component('facebook-page-import-modal', FacebookPageImportModal);
    app.component('facebook-page-actions', FacebookPageActions);
    
    app.mount('#facebookPagesApp');
}

// Initialize the Vue app when DOM is ready
document.addEventListener('DOMContentLoaded', initializeVueApp);
