const FacebookPostList = {
    props: {
        posts: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        totalCount: {
            type: Number,
            default: 0
        },
        currentPage: {
            type: Number,
            default: 1
        },
        pageSize: {
            type: Number,
            default: 10
        },
        canSync: {
            type: Boolean,
            default: false
        },
        canCreateCampaign: {
            type: Boolean,
            default: false
        }
    },
    
    emits: [
        'page-changed',
        'sort-changed',
        'view-on-facebook',
        'create-campaign',
        'view-campaign',
        'sync-post'
    ],
    
    data() {
        return {
            l: null,
            currentSorting: 'facebookCreatedTime desc'
        };
    },
    
    computed: {
        totalPages() {
            return Math.ceil(this.totalCount / this.pageSize);
        },
        
        hasPosts() {
            return this.posts && this.posts.length > 0;
        }
    },
    
    template: `
        <div class="facebook-post-list">
            <!-- Loading State -->
            <div v-if="loading" class="text-center py-4">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">{{ l('Facebook:LoadingPosts') || 'Loading Facebook Posts...' }}</p>
            </div>
            
            <!-- No Posts State -->
            <div v-else-if="!hasPosts" class="alert alert-info">
                <div class="text-center p-4">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <h5>{{ l('Facebook:NoPostsFound') || 'No Posts Found' }}</h5>
                    <p v-if="canSync">{{ l('Facebook:NoPostsMessage') || 'Click "Sync All Posts" to import the latest posts from your Facebook Pages.' }}</p>
                </div>
            </div>
            
            <!-- Posts Table -->
            <div v-else class="facebook-post-card">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 80px;"></th>
                                <th style="width: 120px;">
                                    <a href="#" v-on:click.prevent="sortBy('pageName')" class="text-decoration-none">
                                        {{ l('Facebook:Page') || 'Page' }}
                                        <i class="fas fa-sort ms-1"></i>
                                    </a>
                                </th>
                                <th>{{ l('Facebook:PostContent') || 'Post Content' }}</th>
                                <th style="width: 120px;">
                                    <a href="#" v-on:click.prevent="sortBy('facebookCreatedTime')" class="text-decoration-none">
                                        {{ l('Facebook:Posted') || 'Posted' }}
                                        <i class="fas fa-sort ms-1"></i>
                                    </a>
                                </th>
                                <th style="width: 120px;">{{ l('Facebook:Engagement') || 'Engagement' }}</th>
                                <th style="width: 100px;">
                                    <a href="#" v-on:click.prevent="sortBy('hasActiveCampaign')" class="text-decoration-none">
                                        {{ l('Facebook:Campaign') || 'Campaign' }}
                                        <i class="fas fa-sort ms-1"></i>
                                    </a>
                                </th>
                                <th style="width: 200px;">{{ l('Actions') || 'Actions' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="post in posts" :key="post.id">
                                <!-- Post Thumbnail -->
                                <td>
                                    <img v-if="post.pictureUrl" 
                                         :src="post.pictureUrl" 
                                         :alt="'Post thumbnail'"
                                         class="post-thumbnail">
                                    <img v-else-if="post.attachmentUrl" 
                                         :src="post.attachmentUrl" 
                                         :alt="'Post attachment'"
                                         class="post-thumbnail">
                                    <div v-else class="post-thumbnail-placeholder">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                </td>
                                
                                <!-- Page Name -->
                                <td>
                                    <strong>{{ post.pageName }}</strong>
                                </td>
                                
                                <!-- Post Content -->
                                <td>
                                    <div class="post-content">
                                        <div v-if="post.message" class="post-message">
                                            <span v-if="post.message.length <= 100">{{ post.message }}</span>
                                            <span v-else>{{ post.message.substring(0, 100) }}...</span>
                                        </div>
                                        <div v-else class="text-muted mb-1">[{{ l('Facebook:NoTextContent') || 'No text content' }}]</div>
                                        
                                        <div class="d-flex gap-2 align-items-center">
                                            <span class="post-type-indicator" :class="getPostTypeClass(post.postType)">
                                                {{ getPostTypeLabel(post.postType) }}
                                            </span>
                                            <small v-if="post.attachmentUrl" class="text-info">
                                                {{ l('Facebook:HasAttachment') || 'Has attachment' }}
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                
                                <!-- Posted Date -->
                                <td>
                                    <div>
                                        {{ formatDate(post.facebookCreatedTime) }}
                                        <br>
                                        <small class="text-muted">{{ formatTime(post.facebookCreatedTime) }}</small>
                                    </div>
                                </td>
                                
                                <!-- Engagement Stats -->
                                <td>
                                    <div class="engagement-stats">
                                        <div class="stat-item">👍 {{ formatNumber(post.likesCount) }}</div>
                                        <div class="stat-item">💬 {{ formatNumber(post.commentsCount) }}</div>
                                        <div class="stat-item">🔄 {{ formatNumber(post.sharesCount) }}</div>
                                    </div>
                                </td>
                                
                                <!-- Campaign Status -->
                                <td>
                                    <span v-if="post.hasActiveCampaign" class="campaign-badge campaign-active">
                                        {{ l('Facebook:Active') || 'Active' }}
                                    </span>
                                    <span v-else class="campaign-badge campaign-none">
                                        {{ l('Facebook:None') || 'None' }}
                                    </span>
                                </td>
                                
                                <!-- Actions -->
                                <td>
                                    <facebook-post-actions 
                                        :post="post"
                                        :can-sync="canSync"
                                        :can-create-campaign="canCreateCampaign"
                                        v-on:view-on-facebook="$emit('view-on-facebook', $event)"
                                        v-on:create-campaign="$emit('create-campaign', $event)"
                                        v-on:view-campaign="$emit('view-campaign', $event)"
                                        v-on:sync-post="$emit('sync-post', $event)">
                                    </facebook-post-actions>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center p-3 border-top">
                    <div class="text-muted">
                        {{ l('Facebook:ShowingPosts', (currentPage - 1) * pageSize + 1, Math.min(currentPage * pageSize, totalCount), totalCount) }}
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">
                                    {{ l('Previous') || 'Previous' }}
                                </a>
                            </li>
                            
                            <li v-for="page in getVisiblePages()" 
                                :key="page" 
                                class="page-item" 
                                :class="{ active: page === currentPage }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(page)">
                                    {{ page }}
                                </a>
                            </li>
                            
                            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">
                                    {{ l('Next') || 'Next' }}
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        changePage(page) {
            if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                this.$emit('page-changed', page);
            }
        },
        
        sortBy(field) {
            let newSorting;
            if (this.currentSorting === `${field} asc`) {
                newSorting = `${field} desc`;
            } else {
                newSorting = `${field} asc`;
            }
            
            this.currentSorting = newSorting;
            this.$emit('sort-changed', newSorting);
        },
        
        getVisiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        },
        
        getPostTypeClass(postType) {
            switch (postType?.toLowerCase()) {
                case 'photo':
                    return 'post-type-photo';
                case 'video':
                case 'video_reel':
                    return 'post-type-video';
                case 'link':
                    return 'post-type-link';
                default:
                    return '';
            }
        },
        
        getPostTypeLabel(postType) {
            switch (postType?.toLowerCase()) {
                case 'photo':
                    return this.l('Facebook:Photo') || 'Photo';
                case 'video':
                    return this.l('Facebook:Video') || 'Video';
                case 'video_reel':
                    return this.l('Facebook:Reel') || 'Reel';
                case 'link':
                    return this.l('Facebook:Link') || 'Link';
                case 'status':
                    return this.l('Facebook:Status') || 'Status';
                default:
                    return postType || this.l('Facebook:Post') || 'Post';
            }
        },
        
        formatNumber(num) {
            if (!num) return '0';
            return num.toLocaleString();
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric' 
            });
        },
        
        formatTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },

    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    }
};
