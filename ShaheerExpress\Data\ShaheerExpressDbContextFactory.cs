﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace ShaheerExpress.Data;

public class ShaheerExpressDbContextFactory : IDesignTimeDbContextFactory<ShaheerExpressDbContext>
{
    public ShaheerExpressDbContext CreateDbContext(string[] args)
    {
        ShaheerExpressEfCoreEntityExtensionMappings.Configure();
        var configuration = BuildConfiguration();

        var builder = new DbContextOptionsBuilder<ShaheerExpressDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));

        return new ShaheerExpressDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}