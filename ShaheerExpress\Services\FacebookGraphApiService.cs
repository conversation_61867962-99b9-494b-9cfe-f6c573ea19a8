using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Volo.Abp.DependencyInjection;
using ShaheerExpress.Services.Dtos;

namespace ShaheerExpress.Services;

public class FacebookGraphApiService : ITransientDependency
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<FacebookGraphApiService> _logger;
    private const string GraphApiBaseUrl = "https://graph.facebook.com/v23.0";

    public FacebookGraphApiService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<FacebookGraphApiService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }

    public HttpClient GetHttpClient()
    {
        return _httpClient;
    }

    public async Task<FacebookUserInfo> GetUserInfoAsync(string accessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me?fields=id,name,email,picture&access_token={accessToken}";
            var response = await _httpClient.GetStringAsync(url);
            return JsonConvert.DeserializeObject<FacebookUserInfo>(response)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook user info");
            throw;
        }
    }

    public async Task<List<FacebookPageInfo>> GetUserPagesAsync(string accessToken)
    {
        _logger.LogInformation("Starting GetUserPagesAsync with access token prefix: {TokenPrefix}",
            accessToken?.Substring(0, Math.Min(10, accessToken?.Length ?? 0)) ?? "null");

        try
        {
            var url = $"{GraphApiBaseUrl}/me/accounts?fields=id,name,access_token,picture,category,fan_count&access_token={accessToken}";
            _logger.LogInformation("Making Facebook API request to: {Url}", url.Replace(accessToken, "[REDACTED]"));

            var response = await _httpClient.GetStringAsync(url);
            _logger.LogInformation("Received response from Facebook API. Response length: {ResponseLength}", response?.Length ?? 0);

            var result = JsonConvert.DeserializeObject<FacebookPagesResponse>(response)!;
            var pages = result.Data ?? new List<FacebookPageInfo>();

            _logger.LogInformation("Successfully parsed {PageCount} pages from Facebook API response", pages.Count);

            // Log details about each page (without full access tokens for security)
            foreach (var page in pages)
            {
                var tokenPrefix = page.AccessToken?.Substring(0, Math.Min(10, page.AccessToken?.Length ?? 0)) ?? "null";
                _logger.LogInformation("Page from API: ID={PageId}, Name={PageName}, HasAccessToken={HasToken}, TokenPrefix={TokenPrefix}",
                    page.Id, page.Name, !string.IsNullOrEmpty(page.AccessToken), tokenPrefix);
            }

            return pages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook user pages. Exception: {ExceptionType}, Message: {ErrorMessage}",
                ex.GetType().Name, ex.Message);
            throw;
        }
    }

    public async Task<List<FacebookPostInfo>> GetPagePostsAsync(string pageId, string pageAccessToken, int limit = 25)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/posts?fields=id,message,created_time,attachments,likes.summary(true),comments.summary(true),shares,permalink_url,picture,full_picture&limit={limit}&access_token={pageAccessToken}";
            var response = await _httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<FacebookPostsResponse>(response)!;
            return result.Data ?? new List<FacebookPostInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook page posts");
            throw;
        }
    }

    public async Task<List<FacebookVideoReelInfo>> GetPageVideoReelsAsync(string pageId, string pageAccessToken, int limit = 25)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/video_reels?fields=id,post_id,description,created_time,permalink_url,picture,source,title,likes.summary(true),comments.summary(true),length,thumbnails&limit={limit}&access_token={pageAccessToken}";
            var response = await _httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<FacebookVideoReelsResponse>(response)!;
            return result.Data ?? new List<FacebookVideoReelInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook page video reels");
            throw;
        }
    }

    public async Task<List<FacebookCommentInfo>> GetPostCommentsAsync(string postId, string pageAccessToken, int limit = 30)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{postId}/comments?fields=id,message,created_time,from&limit={limit}&order=reverse_chronological&access_token={pageAccessToken}";
            _logger.LogInformation("Fetching comments for post {PostId} with limit {Limit}", postId, limit);

            var response = await _httpClient.GetStringAsync(url);
            var result = JsonConvert.DeserializeObject<FacebookCommentsResponse>(response)!;
            var comments = result.Data ?? new List<FacebookCommentInfo>();

            _logger.LogInformation("Successfully fetched {CommentCount} comments for post {PostId}", comments.Count, postId);
            return comments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook post comments for post {PostId}", postId);
            throw;
        }
    }

    public async Task<string> PostCommentReplyAsync(string commentId, string message, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{commentId}/comments";
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("message", message),
                new KeyValuePair<string, string>("access_token", pageAccessToken)
            });

            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to post comment reply: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new Exception($"Failed to post comment reply: {response.StatusCode}");
            }

            var result = JsonConvert.DeserializeObject<FacebookCommentResponse>(responseContent)!;
            return result.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error posting comment reply");
            throw;
        }
    }

    public async Task<string> SendPrivateMessageAsync(string commentId, string message, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me/messages?access_token={pageAccessToken}";

            var payload = new {
                recipient = new { comment_id = commentId },
                message = new { text = message } 
            };

            var content = new StringContent(JsonConvert.SerializeObject(payload), Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to send private message: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new Exception($"Failed to send private message: {response.StatusCode}");
            }

            var result = JsonConvert.DeserializeObject<FacebookMessageResponse>(responseContent)!;
            return result.MessageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending private reply for comment ID {CommentId}", commentId);
            throw;
        }
    }

    public async Task<bool> LikeCommentAsync(string commentId, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{commentId}/likes";
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("access_token", pageAccessToken)
            });

            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to like comment: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new Exception($"Failed to like comment: {response.StatusCode}");
            }

            _logger.LogInformation("Successfully liked comment {CommentId}", commentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error liking comment {CommentId}", commentId);
            throw;
        }
    }

    public async Task<bool> SubscribeToWebhookAsync(string pageId, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/subscribed_apps";
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("subscribed_fields", "feed,mention"),
                new KeyValuePair<string, string>("access_token", pageAccessToken)
            });

            var response = await _httpClient.PostAsync(url, content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to webhook");
            throw;
        }
    }

    public async Task<bool> UnsubscribeFromWebhookAsync(string pageId, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/subscribed_apps?access_token={pageAccessToken}";
            var response = await _httpClient.DeleteAsync(url);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from webhook");
            throw;
        }
    }

    public string GetFacebookLoginUrl(string redirectUri)
    {
        var appId = _configuration["Facebook:AppId"];
        var scope = "pages_manage_posts,pages_manage_engagement,pages_manage_metadata,pages_read_engagement,pages_show_list,pages_messaging,public_profile,read_insights,business_management";
        
        return $"https://www.facebook.com/v18.0/dialog/oauth?" +
               $"client_id={appId}&" +
               $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
               $"scope={Uri.EscapeDataString(scope)}&" +
               $"response_type=code";
    }

    public async Task<FacebookTokenResponse> ExchangeCodeForTokenAsync(string code, string redirectUri)
    {
        try
        {
            var appId = _configuration["Facebook:AppId"];
            var appSecret = _configuration["Facebook:AppSecret"];

            var url = $"{GraphApiBaseUrl}/oauth/access_token?" +
                     $"client_id={appId}&" +
                     $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                     $"client_secret={appSecret}&" +
                     $"code={code}";

            var response = await _httpClient.GetStringAsync(url);
            return JsonConvert.DeserializeObject<FacebookTokenResponse>(response)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exchanging code for token");
            throw;
        }
    }

    public async Task<FacebookTokenValidationResult> ValidateAccessTokenAsync(string accessToken)
    {
        try
        {
            var appId = _configuration["Facebook:AppId"];
            var appSecret = _configuration["Facebook:AppSecret"];
            var appAccessToken = $"{appId}|{appSecret}";

            var url = $"{GraphApiBaseUrl}/debug_token?" +
                     $"input_token={accessToken}&" +
                     $"access_token={appAccessToken}";

            var response = await _httpClient.GetStringAsync(url);
            var debugResponse = JsonConvert.DeserializeObject<FacebookTokenDebugResponse>(response)!;

            return ProcessTokenValidationResponse(debugResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Facebook access token");
            return new FacebookTokenValidationResult
            {
                IsValid = false,
                ErrorMessage = "Failed to validate token: " + ex.Message
            };
        }
    }

    public async Task<FacebookTokenValidationResult> ValidatePageAccessTokenAsync(string pageAccessToken)
    {
        try
        {
            var appId = _configuration["Facebook:AppId"];
            var appSecret = _configuration["Facebook:AppSecret"];
            var appAccessToken = $"{appId}|{appSecret}";

            var url = $"{GraphApiBaseUrl}/debug_token?" +
                     $"input_token={pageAccessToken}&" +
                     $"access_token={appAccessToken}";

            var response = await _httpClient.GetStringAsync(url);
            var debugResponse = JsonConvert.DeserializeObject<FacebookTokenDebugResponse>(response)!;

            var result = ProcessTokenValidationResponse(debugResponse);

            // Additional validation for page tokens - check required page scopes
            var requiredPageScopes = new[] { "pages_manage_posts","pages_manage_engagement", "pages_manage_metadata", "pages_read_engagement","pages_show_list", "pages_messaging", "public_profile", "read_insights", "business_management" };
            result.MissingScopes = requiredPageScopes.Where(scope => !result.Scopes.Contains(scope)).ToList();

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Facebook page access token");
            return new FacebookTokenValidationResult
            {
                IsValid = false,
                ErrorMessage = "Failed to validate page token: " + ex.Message
            };
        }
    }

    public async Task<bool> RevokeUserPermissionsAsync(string userAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me/ShaheerExpress?access_token={userAccessToken}";
            var response = await _httpClient.DeleteAsync(url);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully revoked Facebook user ShaheerExpress");
                return true;
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to revoke Facebook ShaheerExpress: {StatusCode} - {Content}",
                response.StatusCode, responseContent);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking Facebook user ShaheerExpress");
            return false;
        }
    }

    private FacebookTokenValidationResult ProcessTokenValidationResponse(FacebookTokenDebugResponse debugResponse)
    {
        var result = new FacebookTokenValidationResult();

        if (debugResponse?.Data == null)
        {
            result.IsValid = false;
            result.ErrorMessage = "Invalid response from Facebook debug endpoint";
            return result;
        }

        var data = debugResponse.Data;

        result.IsValid = data.IsValid;
        result.TokenType = data.Type;
        result.Scopes = data.Scopes ?? new List<string>();

        // Handle expiration
        if (data.ExpiresAt.HasValue && data.ExpiresAt.Value > 0)
        {
            result.ExpiresAt = DateTimeOffset.FromUnixTimeSeconds(data.ExpiresAt.Value).DateTime;
            result.IsExpired = result.ExpiresAt <= DateTime.UtcNow;
        }

        // Handle errors
        if (data.Error != null)
        {
            result.ErrorMessage = data.Error.Message;
            result.ErrorCode = data.Error.Code;
            result.IsValid = false;
        }

        // Validate required scopes for user tokens
        if (data.Type == "USER")
        {
            var requiredUserScopes = new[] { "pages_manage_posts", "pages_manage_engagement", "pages_manage_metadata", "pages_read_engagement", "pages_show_list", "pages_messaging", "public_profile", "read_insights", "business_management" };
            result.MissingScopes = requiredUserScopes.Where(scope => !result.Scopes.Contains(scope)).ToList();
        }

        return result;
    }

    public async Task<string> SendPrivateMessageWithTemplateAsync(string commentId, string messageText, string? imageUrl, string postUrl, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me/messages?access_token={pageAccessToken}";

            var elements = new List<object>();
            if (!string.IsNullOrEmpty(imageUrl))
            {
                elements.Add(new {
                    title = messageText,
                    image_url = imageUrl,
                    default_action = new {
                        type = "web_url",
                        url = postUrl
                    },
                    buttons = new[] {
                        new {
                            type = "web_url",
                            url = postUrl,
                            title = "View Post"
                        }
                    }
                });
            }
            else
            {
                // Button template if no image
                var buttons = new[] {
                    new {
                        type = "web_url",
                        url = postUrl,
                        title = "View Post"
                    }
                };
                var payload = new {
                    recipient = new { comment_id = commentId },
                    message = new {
                        attachment = new {
                            type = "template",
                            payload = new {
                                template_type = "button",
                                text = messageText,
                                buttons = buttons
                            }
                        }
                    }
                };
                var contentButton = new StringContent(JsonConvert.SerializeObject(payload), Encoding.UTF8, "application/json");
                var responseButton = await _httpClient.PostAsync(url, contentButton);
                var responseContentButton = await responseButton.Content.ReadAsStringAsync();
                if (!responseButton.IsSuccessStatusCode)
                {
                    _logger.LogError("Failed to send private message with button template: {StatusCode} - {Content}", responseButton.StatusCode, responseContentButton);
                    throw new Exception($"Failed to send private message with button template: {responseButton.StatusCode}");
                }
                var resultButton = JsonConvert.DeserializeObject<FacebookMessageResponse>(responseContentButton)!;
                return resultButton.MessageId;
            }

            // Generic template with image
            var genericPayload = new {
                recipient = new { comment_id = commentId },
                message = new {
                    attachment = new {
                        type = "template",
                        payload = new {
                            template_type = "generic",
                            elements = elements
                        }
                    }
                }
            };
            var content = new StringContent(JsonConvert.SerializeObject(genericPayload), Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to send private message with generic template: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new Exception($"Failed to send private message with generic template: {response.StatusCode}");
            }
            var result = JsonConvert.DeserializeObject<FacebookMessageResponse>(responseContent)!;
            return result.MessageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending private reply with template for comment ID {CommentId}", commentId);
            throw;
        }
    }

    public async Task<string> SendPrivateCardReplyAsync(string commentId, CardReplyData cardReplyData, string postUrl, string pageAccessToken)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/me/messages?access_token={pageAccessToken}";

            // Build the Facebook generic template element
            object element;
            var filteredButtons = cardReplyData.Buttons
                .Select(button => new
                {
                    type = button.Type == CardReplyButtonType.WebUrl ? "web_url" : "phone_number",
                    title = button.Title,
                    url = button.Type == CardReplyButtonType.WebUrl ? button.Url : null,
                    payload = button.Type == CardReplyButtonType.PhoneNumber ? button.PhoneNumber : null
                })
                .Where(b => (b.url != null && b.type == "web_url") || (b.payload != null && b.type == "phone_number"))
                .ToArray();

            if (filteredButtons.Length > 0)
            {
                element = new
                {
                    title = cardReplyData.Title,
                    subtitle = cardReplyData.Subtitle,
                    image_url = cardReplyData.ImageUrl,
                    default_action = new
                    {
                        type = "web_url",
                        url = postUrl
                    },
                    buttons = filteredButtons
                };
            }
            else
            {
                element = new
                {
                    title = cardReplyData.Title,
                    subtitle = cardReplyData.Subtitle,
                    image_url = cardReplyData.ImageUrl,
                    default_action = new
                    {
                        type = "web_url",
                        url = postUrl
                    }
                };
            }

            var payload = new
            {
                recipient = new { comment_id = commentId },
                message = new
                {
                    attachment = new
                    {
                        type = "template",
                        payload = new
                        {
                            template_type = "generic",
                            elements = new[] { element }
                        }
                    }
                }
            };

            var content = new StringContent(JsonConvert.SerializeObject(payload), Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to send private card reply: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new Exception($"Failed to send private card reply: {response.StatusCode}");
            }

            var result = JsonConvert.DeserializeObject<FacebookMessageResponse>(responseContent)!;
            return result.MessageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending private card reply for comment ID {CommentId}", commentId);
            throw;
        }
    }

    // Facebook Post Publishing Methods

    /// <summary>
    /// Publishes a post to a Facebook Page
    /// </summary>
    public async Task<string> PublishPostAsync(string pageId, string pageAccessToken, FacebookPostPublishRequest request)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/feed";
            var parameters = new List<KeyValuePair<string, string>>
            {
                new("access_token", pageAccessToken),
                new("published", request.Published.ToString().ToLower())
            };

            if (!string.IsNullOrEmpty(request.Message))
            {
                parameters.Add(new("message", request.Message));
            }

            if (!string.IsNullOrEmpty(request.Link))
            {
                parameters.Add(new("link", request.Link));
            }

            if (request.MediaIds.Any())
            {
                // For multiple media, use attached_media parameter
                var attachedMedia = request.MediaIds.Select(id => $"{{\"media_fbid\":\"{id}\"}}").ToArray();
                parameters.Add(new("attached_media", $"[{string.Join(",", attachedMedia)}]"));
            }

            if (request.ScheduledPublishTime.HasValue)
            {
                parameters.Add(new("scheduled_publish_time", request.ScheduledPublishTime.Value.ToString()));
            }

            var content = new FormUrlEncodedContent(parameters);
            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to publish post to page {PageId}: {StatusCode} - {Content}",
                    pageId, response.StatusCode, responseContent);
                throw new Exception($"Failed to publish post: {response.StatusCode} - {responseContent}");
            }

            var result = JsonConvert.DeserializeObject<FacebookPostPublishResponse>(responseContent)!;
            _logger.LogInformation("Successfully published post {PostId} to page {PageId}", result.Id, pageId);

            return result.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing post to page {PageId}", pageId);
            throw;
        }
    }

    /// <summary>
    /// Uploads media (image or video) to Facebook for use in posts
    /// </summary>
    public async Task<string> UploadMediaAsync(string pageId, string pageAccessToken, byte[] mediaContent, string contentType, string fileName)
    {
        try
        {
            var url = $"{GraphApiBaseUrl}/{pageId}/photos";

            // For videos, use different endpoint
            if (contentType.StartsWith("video/"))
            {
                url = $"{GraphApiBaseUrl}/{pageId}/videos";
            }

            using var form = new MultipartFormDataContent();
            form.Add(new StringContent(pageAccessToken), "access_token");
            form.Add(new StringContent("true"), "published"); // Set to false for unpublished media

            var mediaContent2 = new ByteArrayContent(mediaContent);
            mediaContent2.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(contentType);

            if (contentType.StartsWith("video/"))
            {
                form.Add(mediaContent2, "source", fileName);
            }
            else
            {
                form.Add(mediaContent2, "source", fileName);
            }

            var response = await _httpClient.PostAsync(url, form);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to upload media to page {PageId}: {StatusCode} - {Content}",
                    pageId, response.StatusCode, responseContent);
                throw new Exception($"Failed to upload media: {response.StatusCode} - {responseContent}");
            }

            var result = JsonConvert.DeserializeObject<FacebookMediaUploadResponse>(responseContent)!;
            _logger.LogInformation("Successfully uploaded media {MediaId} to page {PageId}", result.Id, pageId);

            return result.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading media to page {PageId}", pageId);
            throw;
        }
    }

    /// <summary>
    /// Gets link preview information from Facebook
    /// </summary>
    public async Task<FacebookLinkPreviewInfo> GetLinkPreviewAsync(string url, string accessToken)
    {
        try
        {
            var apiUrl = $"{GraphApiBaseUrl}/?id={Uri.EscapeDataString(url)}&fields=og_object{{id,description,title,type,updated_time,url,image}}&access_token={accessToken}";

            var response = await _httpClient.GetStringAsync(apiUrl);
            var result = JsonConvert.DeserializeObject<FacebookLinkPreviewResponse>(response)!;

            return new FacebookLinkPreviewInfo
            {
                Url = url,
                Title = result.OgObject?.Title,
                Description = result.OgObject?.Description,
                ImageUrl = result.OgObject?.Image?.FirstOrDefault()?.Url,
                IsValid = result.OgObject != null
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting link preview for URL {Url}", url);
            return new FacebookLinkPreviewInfo
            {
                Url = url,
                IsValid = false
            };
        }
    }
}
