using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace ShaheerExpress.Authors
{
    public class AuthorUpdateDto : IHasConcurrencyStamp
    {
        [Required]
        public string Name { get; set; } = null!;
        public DateOnly Birthdate { get; set; }
        public string? Bio { get; set; }

        public string ConcurrencyStamp { get; set; } = null!;
    }
}