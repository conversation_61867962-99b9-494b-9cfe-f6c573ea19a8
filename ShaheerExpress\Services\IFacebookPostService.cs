using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ShaheerExpress.Services.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace ShaheerExpress.Services;

public interface IFacebookPostService : IApplicationService
{
    Task<PagedResultDto<FacebookPostDto>> GetListAsync(GetPostsInput input);
    Task<FacebookPostDto> GetAsync(Guid id);
    Task<FacebookPostDto> CreateAsync(CreateFacebookPostDto input);
    Task<FacebookPostDto> UpdateAsync(Guid id, UpdateFacebookPostDto input);
    Task DeleteAsync(Guid id);
    
    Task SyncPostsFromPageAsync(Guid pageId);
    Task SyncAllPostsAsync();
    Task<PagedResultDto<FacebookPostDto>> GetPostsForCampaignCreationAsync(GetPostsInput input);
    Task<List<FacebookPostSelectionDto>> GetPostsForSelectionAsync(GetPostsForSelectionInput input);
}
