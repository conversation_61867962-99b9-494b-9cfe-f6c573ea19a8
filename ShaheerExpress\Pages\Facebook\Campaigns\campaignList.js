const CampaignList = {
    props: {
        campaigns: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        totalCount: {
            type: Number,
            default: 0
        },
        currentPage: {
            type: Number,
            default: 1
        },
        pageSize: {
            type: Number,
            default: 10
        },
        canEdit: {
            type: Boolean,
            default: false
        },
        canDelete: {
            type: Boolean,
            default: false
        },
        canActivate: {
            type: Boolean,
            default: false
        },
        canDeactivate: {
            type: Boolean,
            default: false
        }
    },
    
    emits: [
        'page-changed',
        'sort-changed',
        'activate-campaign',
        'deactivate-campaign',
        'edit-campaign',
        'delete-campaign',
        'view-details',
        'view-activities'
    ],
    
    data() {
        return {
            l: null,
            currentSorting: 'creationTime desc'
        };
    },
    
    computed: {
        totalPages() {
            return Math.ceil(this.totalCount / this.pageSize);
        },
        
        hasCampaigns() {
            return this.campaigns && this.campaigns.length > 0;
        }
    },
    
    template: `
        <div class="campaign-list">
            <!-- Loading State -->
            <div v-if="loading" class="text-center py-4">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">{{ l('Facebook:LoadingCampaigns') || 'Loading campaigns...' }}</p>
            </div>
            
            <!-- No Campaigns State -->
            <div v-else-if="!hasCampaigns" class="alert alert-info">
                <div class="text-center p-4">
                    <i class="fas fa-cogs fa-3x mb-3"></i>
                    <h5>{{ l('Facebook:NoCampaignsFound') || 'No Campaigns Found' }}</h5>
                    <p>{{ l('Facebook:NoCampaignsMessage') || 'Create your first auto-reply campaign to get started.' }}</p>
                </div>
            </div>
            
            <!-- Campaigns Table -->
            <div v-else class="facebook-campaign-card">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 120px;">
                                    <a href="#" v-on:click.prevent="sortBy('pageName')" class="text-decoration-none">
                                        {{ l('Facebook:Page') || 'Page' }}
                                        <i class="fas fa-sort ms-1"></i>
                                    </a>
                                </th>
                                <th>{{ l('Facebook:CampaignName') || 'Campaign Name' }}</th>
                                <th style="width: 200px;">{{ l('Facebook:Post') || 'Post' }}</th>
                                <th style="width: 100px;">
                                    <a href="#" v-on:click.prevent="sortBy('isActive')" class="text-decoration-none">
                                        {{ l('Facebook:Status') || 'Status' }}
                                        <i class="fas fa-sort ms-1"></i>
                                    </a>
                                </th>
                                <th style="width: 120px;">{{ l('Facebook:Performance') || 'Performance' }}</th>
                                <th style="width: 120px;">
                                    <a href="#" v-on:click.prevent="sortBy('creationTime')" class="text-decoration-none">
                                        {{ l('Facebook:Created') || 'Created' }}
                                        <i class="fas fa-sort ms-1"></i>
                                    </a>
                                </th>
                                <th style="width: 200px;">{{ l('Actions') || 'Actions' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="campaign in campaigns" :key="campaign.id">
                                <!-- Page Name -->
                                <td>
                                    <strong>{{ campaign.pageName }}</strong>
                                </td>
                                
                                <!-- Campaign Name -->
                                <td>
                                    <div>
                                        <strong>{{ campaign.campaignName }}</strong>
                                        <br>
                                        <div class="d-flex gap-1 mt-1">
                                            <span v-if="campaign.sendPublicReply" class="reply-type-indicator reply-type-public">
                                                {{ l('Facebook:PublicReply') || 'Public' }}
                                            </span>
                                            <span v-if="campaign.sendPrivateReply" class="reply-type-indicator reply-type-private">
                                                {{ l('Facebook:PrivateReply') || 'Private' }}
                                            </span>
                                            <span v-if="campaign.sendLike" class="reply-type-indicator">
                                                <i class="fas fa-thumbs-up"></i> {{ l('Facebook:Like') || 'Like' }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                
                                <!-- Post Preview -->
                                <td>
                                    <div class="campaign-post-preview" v-if="campaign.postMessage">
                                        <div class="campaign-post-message">{{ campaign.postMessage }}</div>
                                        <small class="text-muted">{{ formatDate(campaign.postCreatedTime) }}</small>
                                    </div>
                                    <div v-else class="text-muted">
                                        <small>{{ l('Facebook:NoPostSelected') || 'No post selected' }}</small>
                                    </div>
                                </td>
                                
                                <!-- Status -->
                                <td>
                                    <span v-if="campaign.isActive" class="campaign-status-badge status-active">
                                        {{ l('Facebook:Active') || 'Active' }}
                                    </span>
                                    <span v-else-if="campaign.endDate && new Date(campaign.endDate) < new Date()" class="campaign-status-badge status-ended">
                                        {{ l('Facebook:Ended') || 'Ended' }}
                                    </span>
                                    <span v-else class="campaign-status-badge status-inactive">
                                        {{ l('Facebook:Inactive') || 'Inactive' }}
                                    </span>
                                </td>
                                
                                <!-- Performance Metrics -->
                                <td>
                                    <div class="campaign-metrics">
                                        <div class="metric-item">
                                            <div class="metric-value">{{ formatNumber(campaign.totalRepliesSent || 0) }}</div>
                                            <div class="metric-label">{{ l('Facebook:Replies') || 'Replies' }}</div>
                                        </div>
                                        <div class="metric-item" v-if="campaign.sendLike">
                                            <div class="metric-value">{{ formatNumber(campaign.likesSent || 0) }}</div>
                                            <div class="metric-label">{{ l('Facebook:Likes') || 'Likes' }}</div>
                                        </div>
                                    </div>
                                </td>
                                
                                <!-- Created Date -->
                                <td>
                                    <div>
                                        {{ formatDate(campaign.creationTime) }}
                                        <br>
                                        <small class="text-muted">{{ formatTime(campaign.creationTime) }}</small>
                                    </div>
                                </td>
                                
                                <!-- Actions -->
                                <td>
                                    <campaign-actions 
                                        :campaign="campaign"
                                        :can-edit="canEdit"
                                        :can-delete="canDelete"
                                        :can-activate="canActivate"
                                        :can-deactivate="canDeactivate"
                                        v-on:activate-campaign="$emit('activate-campaign', $event)"
                                        v-on:deactivate-campaign="$emit('deactivate-campaign', $event)"
                                        v-on:edit-campaign="$emit('edit-campaign', $event)"
                                        v-on:delete-campaign="$emit('delete-campaign', $event)"
                                        v-on:view-details="$emit('view-details', $event)"
                                        v-on:view-activities="$emit('view-activities', $event)">
                                    </campaign-actions>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center p-3 border-top">
                    <div class="text-muted">
                        {{ l('Facebook:ShowingCampaigns', (currentPage - 1) * pageSize + 1, Math.min(currentPage * pageSize, totalCount), totalCount) }}
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">
                                    {{ l('Previous') || 'Previous' }}
                                </a>
                            </li>
                            
                            <li v-for="page in getVisiblePages()" 
                                :key="page" 
                                class="page-item" 
                                :class="{ active: page === currentPage }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(page)">
                                    {{ page }}
                                </a>
                            </li>
                            
                            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">
                                    {{ l('Next') || 'Next' }}
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        changePage(page) {
            if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                this.$emit('page-changed', page);
            }
        },
        
        sortBy(field) {
            let newSorting;
            if (this.currentSorting === `${field} asc`) {
                newSorting = `${field} desc`;
            } else {
                newSorting = `${field} asc`;
            }
            
            this.currentSorting = newSorting;
            this.$emit('sort-changed', newSorting);
        },
        
        getVisiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        },
        
        formatNumber(num) {
            if (!num) return '0';
            return num.toLocaleString();
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric' 
            });
        },
        
        formatTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },

    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    }
};
