@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using ShaheerExpress.Permissions
@using ShaheerExpress.Pages.Facebook.Pages
@using ShaheerExpress.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using ShaheerExpress.Localization
@inject IHtmlLocalizer<ShaheerExpressResource> L
@inject IAuthorizationService Authorization
@model ShaheerExpress.Pages.Facebook.Pages.IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Facebook:Pages"].Value;
    PageLayout.Content.MenuItemName = ShaheerExpressMenus.FacebookPages;
}

@section styles
{
    <style>
        .facebook-page-card {
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .page-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .page-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .page-status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        
        .status-connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .import-page-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease-in-out;
        }
        
        .import-page-item:hover {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .import-page-item.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        .token-status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .token-valid {
            background-color: #28a745;
        }
        
        .token-expired {
            background-color: #dc3545;
        }
        
        .token-warning {
            background-color: #ffc107;
        }
        
        .page-actions-dropdown {
            min-width: 180px;
        }
        
        .webhook-indicator {
            font-size: 0.75rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            background-color: #e9ecef;
            color: #495057;
        }
        
        .webhook-active {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
}

@section scripts
{
    <abp-script src="/libs/vue/vue.global.js" />
    <abp-script src="/Pages/Facebook/Pages/facebookPageList.js" />
    <abp-script src="/Pages/Facebook/Pages/facebookPageImportModal.js" />
    <abp-script src="/Pages/Facebook/Pages/facebookPageActions.js" />
    <abp-script src="/Pages/Facebook/Pages/index.js" />
}

@section content_toolbar {
    <div id="facebookPagesToolbar" v-if="isConnected">
        <div class="d-flex gap-2">
            <button type="button" 
                    class="btn btn-outline-info btn-sm" 
                    v-on:click="validateAllPageTokens" 
                    :disabled="isValidatingTokens">
                <div v-if="isValidatingTokens" class="loading-spinner me-1"></div>
                <i v-else class="fas fa-shield-alt me-1"></i>
                @L["Facebook:ValidateTokens"]
            </button>
            <button type="button" 
                    class="btn btn-primary btn-sm" 
                    v-on:click="openImportModal" 
                    v-if="canManagePages">
                <i class="fas fa-download me-1"></i>
                @L["Facebook:ImportPages"]
            </button>
            <button type="button" 
                    class="btn btn-secondary btn-sm" 
                    v-on:click="syncAllPages" 
                    v-if="canManagePages">
                <i class="fas fa-sync-alt me-1"></i>
                @L["Facebook:SyncAll"]
            </button>
        </div>
    </div>
}

<div id="facebookPagesApp">
    <!-- Loading State -->
    <div v-if="loading" class="text-center py-4">
        <div class="loading-spinner"></div>
        <p class="mt-2 text-muted">{{ loadingMessage }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fas fa-file-alt text-primary me-2"></i>
                            @L["Facebook:Pages"]
                        </h2>
                        <p class="text-muted mb-0">@L["Facebook:PagesDescription"]</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Token Status Alert -->
        <div v-if="isConnected && pageTokenStatuses && Object.keys(pageTokenStatuses).length > 0" class="mb-4">
            <div v-if="tokenIssueCount > 0" class="alert alert-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>@L["Facebook:TokenIssuesDetected"]</strong>
                        <br>
                        <span>{{ l('Facebook:TokenIssuesMessage', tokenIssueCount, totalPagesCount) }}</span>
                        <a href="/Facebook" class="alert-link ms-2">@L["Facebook:ReconnectAccount"]</a>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" v-on:click="navigateToConnection">
                        <i class="fas fa-link me-1"></i>
                        @L["Facebook:ReconnectFacebook"]
                    </button>
                </div>
            </div>
            <div v-else-if="validPagesCount === totalPagesCount && totalPagesCount > 0" class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                {{ l('Facebook:AllTokensValid', totalPagesCount) }}
            </div>
        </div>

        <!-- Connection Required -->
        <div v-if="!isConnected" class="alert alert-info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>@L["Facebook:ConnectionRequired"]</strong>
                    <br>
                    <span>@L["Facebook:ConnectionRequiredMessage"]</span>
                </div>
                <button type="button" class="btn btn-primary btn-sm" v-on:click="navigateToConnection">
                    <i class="fas fa-link me-1"></i>
                    @L["Facebook:ConnectNow"]
                </button>
            </div>
        </div>

        <!-- Facebook Page List Component -->
        <facebook-page-list 
            v-if="isConnected"
            ref="pageListComponent"
            :pages="pages"
            :loading="pagesLoading"
            :total-count="totalCount"
            :current-page="currentPage"
            :page-size="pageSize"
            :can-manage="canManagePages"
            :page-token-statuses="pageTokenStatuses"
            v-on:page-changed="handlePageChanged"
            v-on:sync-page="handleSyncPage"
            v-on:disconnect-page="handleDisconnectPage"
            v-on:reconnect-page="handleReconnectPage"
            v-on:subscribe-webhook="handleSubscribeWebhook"
            v-on:unsubscribe-webhook="handleUnsubscribeWebhook"
            v-on:create-campaign="handleCreateCampaign">
        </facebook-page-list>

        <!-- Import Pages Modal -->
        <facebook-page-import-modal 
            ref="importModalComponent"
            v-on:pages-imported="handlePagesImported">
        </facebook-page-import-modal>
    </div>
</div>
