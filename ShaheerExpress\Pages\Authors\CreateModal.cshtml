﻿@page
@using Microsoft.AspNetCore.Mvc.Localization
@using ShaheerExpress.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using ShaheerExpress.Pages.Authors
@using ShaheerExpress.Authors;
@using System.Globalization
@inject IHtmlLocalizer<ShaheerExpressResource> L
@model ShaheerExpress.Pages.Authors.CreateModalModel
@{
    Layout = null;
}

<form data-ajaxForm="true" asp-page="/Authors/CreateModal" autocomplete="off">
    <abp-modal id="AuthorCreateModal">
        <abp-modal-header title="@L["NewAuthor"].Value"></abp-modal-header>

        <abp-modal-body>





            <abp-input asp-for="Author.Name" />


            <abp-input asp-for="Author.Birthdate" type="date" value="@DateTime.Now" />


            <abp-input asp-for="Author.Bio" text-area />





        </abp-modal-body>

        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)">

        </abp-modal-footer>
    </abp-modal>
</form>