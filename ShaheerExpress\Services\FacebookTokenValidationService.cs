using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ShaheerExpress.Entities;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace ShaheerExpress.Services;

public class FacebookTokenValidationService : BackgroundService, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<FacebookTokenValidationService> _logger;
    private readonly TimeSpan _validationInterval = TimeSpan.FromHours(6); // Check every 6 hours

    public FacebookTokenValidationService(
        IServiceProvider serviceProvider,
        ILogger<FacebookTokenValidationService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Facebook Token Validation Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ValidateAllTokensAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Facebook token validation cycle");
            }

            // Wait for the next validation cycle
            await Task.Delay(_validationInterval, stoppingToken);
        }

        _logger.LogInformation("Facebook Token Validation Service stopped");
    }

    private async Task ValidateAllTokensAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var unitOfWorkManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();
        
        using var uow = unitOfWorkManager.Begin();
        
        try
        {
            var facebookUserRepository = scope.ServiceProvider.GetRequiredService<IRepository<FacebookUser, Guid>>();
            var facebookPageRepository = scope.ServiceProvider.GetRequiredService<IRepository<FacebookPage, Guid>>();
            var graphApiService = scope.ServiceProvider.GetRequiredService<FacebookGraphApiService>();

            // Get all active Facebook users
            var activeUsers = await facebookUserRepository.GetListAsync(x => x.IsActive);
            
            _logger.LogInformation("Starting token validation for {UserCount} active Facebook users", activeUsers.Count);

            foreach (var user in activeUsers)
            {
                await ValidateUserTokenAsync(user, graphApiService, facebookUserRepository, facebookPageRepository);
            }

            await uow.CompleteAsync();
            
            _logger.LogInformation("Completed token validation cycle for {UserCount} users", activeUsers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token validation cycle");
            throw;
        }
    }

    private async Task ValidateUserTokenAsync(
        FacebookUser user, 
        FacebookGraphApiService graphApiService,
        IRepository<FacebookUser, Guid> facebookUserRepository,
        IRepository<FacebookPage, Guid> facebookPageRepository)
    {
        try
        {
            _logger.LogDebug("Validating token for user {UserId} (Facebook ID: {FacebookId})", 
                user.UserId, user.FacebookId);

            // Validate user access token
            var userTokenValidation = await graphApiService.ValidateAccessTokenAsync(user.AccessToken);
            
            if (!userTokenValidation.IsValid || userTokenValidation.IsExpired)
            {
                _logger.LogWarning("Invalid or expired user token for user {UserId}. Error: {Error}", 
                    user.UserId, userTokenValidation.ErrorMessage);
                
                // Deactivate the user's Facebook connection
                user.Deactivate();
                await facebookUserRepository.UpdateAsync(user);
                
                // Also deactivate all associated pages
                var userPages = await facebookPageRepository.GetListAsync(p => p.FacebookUserId == user.Id);
                foreach (var page in userPages)
                {
                    page.Disconnect();
                    await facebookPageRepository.UpdateAsync(page);
                }
                
                _logger.LogInformation("Deactivated Facebook connection for user {UserId} due to invalid token", user.UserId);
            }

            // Validate page access tokens
            var connectedUserPages = await facebookPageRepository.GetListAsync(p => p.FacebookUserId == user.Id && p.IsConnected);
            
            foreach (var page in connectedUserPages)
            {
                await ValidatePageTokenAsync(page, graphApiService, facebookPageRepository);
            }

            _logger.LogDebug("Token validation completed for user {UserId}", user.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tokens for user {UserId}", user.UserId);
        }
    }

    private async Task ValidatePageTokenAsync(
        FacebookPage page,
        FacebookGraphApiService graphApiService,
        IRepository<FacebookPage, Guid> facebookPageRepository)
    {
        try
        {
            var pageTokenValidation = await graphApiService.ValidatePageAccessTokenAsync(page.PageAccessToken);
            
            if (!pageTokenValidation.IsValid || pageTokenValidation.IsExpired)
            {
                _logger.LogWarning("Invalid or expired page token for page {PageId} (Facebook Page ID: {FacebookPageId}). Error: {Error}", 
                    page.Id, page.FacebookPageId, pageTokenValidation.ErrorMessage);
                
                page.Disconnect();
                await facebookPageRepository.UpdateAsync(page);
                
                _logger.LogInformation("Disconnected Facebook page {PageId} due to invalid token", page.Id);
            }
            else if (pageTokenValidation.MissingScopes.Any())
            {
                _logger.LogWarning("Page {PageId} is missing required scopes: {MissingScopes}", 
                    page.Id, string.Join(", ", pageTokenValidation.MissingScopes));
                
                // You might want to mark the page as having issues but not disconnect it
                // This depends on your business logic
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating page token for page {PageId}", page.Id);
        }
    }
}

public class FacebookTokenValidationJob : ITransientDependency
{
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly IRepository<FacebookPage, Guid> _facebookPageRepository;
    private readonly FacebookGraphApiService _graphApiService;
    private readonly ILogger<FacebookTokenValidationJob> _logger;

    public FacebookTokenValidationJob(
        IRepository<FacebookUser, Guid> facebookUserRepository,
        IRepository<FacebookPage, Guid> facebookPageRepository,
        FacebookGraphApiService graphApiService,
        ILogger<FacebookTokenValidationJob> logger)
    {
        _facebookUserRepository = facebookUserRepository;
        _facebookPageRepository = facebookPageRepository;
        _graphApiService = graphApiService;
        _logger = logger;
    }

    public async Task ValidateTokensAsync()
    {
        try
        {
            var activeUsers = await _facebookUserRepository.GetListAsync(x => x.IsActive);
            
            _logger.LogInformation("Starting manual token validation for {UserCount} active Facebook users", activeUsers.Count);

            foreach (var user in activeUsers)
            {
                await ValidateUserTokenAsync(user);
            }
            
            _logger.LogInformation("Completed manual token validation for {UserCount} users", activeUsers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual token validation");
            throw;
        }
    }

    private async Task ValidateUserTokenAsync(FacebookUser user)
    {
        try
        {
            var userTokenValidation = await _graphApiService.ValidateAccessTokenAsync(user.AccessToken);
            
            if (!userTokenValidation.IsValid || userTokenValidation.IsExpired)
            {
                user.Deactivate();
                await _facebookUserRepository.UpdateAsync(user);
                
                var userPages = await _facebookPageRepository.GetListAsync(p => p.FacebookUserId == user.Id);
                foreach (var page in userPages)
                {
                    page.Disconnect();
                    await _facebookPageRepository.UpdateAsync(page);
                }
                
                _logger.LogInformation("Deactivated Facebook connection for user {UserId} due to invalid token", user.UserId);
                return;
            }

            var connectedUserPages = await _facebookPageRepository.GetListAsync(p => p.FacebookUserId == user.Id && p.IsConnected);
            
            foreach (var page in connectedUserPages)
            {
                var pageTokenValidation = await _graphApiService.ValidatePageAccessTokenAsync(page.PageAccessToken);
                
                if (!pageTokenValidation.IsValid || pageTokenValidation.IsExpired)
                {
                    page.Disconnect();
                    await _facebookPageRepository.UpdateAsync(page);
                    
                    _logger.LogInformation("Disconnected Facebook page {PageId} due to invalid token", page.Id);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tokens for user {UserId}", user.UserId);
        }
    }
}
