﻿using ShaheerExpress.Entities;
using ShaheerExpress.Services;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace ShaheerExpress.BackgroundJobs
{
    public class ProcessOldCommentsJob : AsyncBackgroundJob<ProcessOldCommentsJobArgs>, ITransientDependency
    {
        private readonly ILogger<ProcessOldCommentsJob> _logger;
        private readonly IRepository<AutoReplyCampaign, Guid> _campaignRepository;
        private readonly IRepository<FacebookPage, Guid> _pageRepository;
        private readonly IRepository<CampaignActivity, Guid> _activityRepository;
        private readonly FacebookGraphApiService _facebookGraphApiService;
        private readonly IBackgroundJobManager _backgroundJobManager;
        private readonly IConfiguration _configuration;

        public ProcessOldCommentsJob(
            ILogger<ProcessOldCommentsJob> logger,
            IRepository<AutoReplyCampaign, Guid> campaignRepository,
            IRepository<FacebookPage, Guid> pageRepository,
            IRepository<CampaignActivity, Guid> activityRepository,
            FacebookGraphApiService facebookGraphApiService,
            IBackgroundJobManager backgroundJobManager,
            IConfiguration configuration)
        {
            _logger = logger;
            _campaignRepository = campaignRepository;
            _pageRepository = pageRepository;
            _activityRepository = activityRepository;
            _facebookGraphApiService = facebookGraphApiService;
            _backgroundJobManager = backgroundJobManager;
            _configuration = configuration;
        }

        [UnitOfWork]
        public override async Task ExecuteAsync(ProcessOldCommentsJobArgs args)
        {
            try
            {
                _logger.LogInformation("Starting to process old comments for campaign {CampaignId}", args.CampaignId);

                // Check if feature is enabled
                var isEnabled = _configuration.GetValue<bool>("AutoReply:ReplyToOldCommentsEnabled", true);
                if (!isEnabled)
                {
                    _logger.LogInformation("Old comments processing is disabled in configuration");
                    return;
                }

                // Get the campaign
                var campaign = await _campaignRepository.GetAsync(args.CampaignId);
                if (!campaign.IsValidForReply())
                {
                    _logger.LogWarning("Campaign {CampaignId} is not valid for replies", args.CampaignId);
                    return;
                }

                // Get the Facebook page
                var page = await _pageRepository.FirstOrDefaultAsync(p => p.FacebookPageId == campaign.FacebookPageIdString);
                if (page == null)
                {
                    _logger.LogError("Facebook page not found for campaign {CampaignId}", args.CampaignId);
                    return;
                }

                // Get configuration for max comments to process
                var maxComments = _configuration.GetValue<int>("AutoReply:MaxOldCommentsToReply", 30);

                _logger.LogInformation("Fetching up to {MaxComments} old comments for post {PostId}",
                    maxComments, campaign.FacebookPostIdString);

                // Fetch existing comments from Facebook
                var comments = await _facebookGraphApiService.GetPostCommentsAsync(
                    campaign.FacebookPostIdString, page.PageAccessToken, maxComments);

                if (!comments.Any())
                {
                    _logger.LogInformation("No comments found for post {PostId}", campaign.FacebookPostIdString);
                    return;
                }

                _logger.LogInformation("Found {CommentCount} comments to process for campaign {CampaignId}",
                    comments.Count, args.CampaignId);

                // Process each comment with delay to respect rate limits
                var processedCount = 0;
                foreach (var comment in comments)
                {
                    try
                    {
                        // Check if we've already processed this comment
                        var existingActivity = await _activityRepository.FirstOrDefaultAsync(
                            a => a.FacebookCommentId == comment.Id);

                        if (existingActivity != null)
                        {
                            _logger.LogDebug("Comment {CommentId} already processed, skipping", comment.Id);
                            continue;
                        }

                        // Queue individual auto-reply job for this comment
                        await _backgroundJobManager.EnqueueAsync(new ProcessAutoReplyJobArgs
                        {
                            CampaignId = campaign.Id,
                            CommentId = comment.Id,
                            CommenterFacebookId = comment.From?.Id ?? string.Empty,
                            CommenterName = comment.From?.Name ?? string.Empty,
                            OriginalComment = comment.Message,
                            CommentCreatedAt = comment.CreatedTime
                        });

                        processedCount++;
                        _logger.LogDebug("Queued auto-reply job for old comment {CommentId}", comment.Id);

                        // Add small delay to respect rate limits (100ms between jobs)
                        await Task.Delay(100);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing old comment {CommentId} for campaign {CampaignId}",
                            comment.Id, args.CampaignId);
                        // Continue processing other comments
                    }
                }

                _logger.LogInformation("Successfully queued {ProcessedCount} old comments for processing in campaign {CampaignId}",
                    processedCount, args.CampaignId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing old comments for campaign {CampaignId}", args.CampaignId);
                throw;
            }
        }
    }

    public class ProcessOldCommentsJobArgs
    {
        public Guid CampaignId { get; set; }
    }
}
