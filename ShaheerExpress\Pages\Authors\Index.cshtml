﻿@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using ShaheerExpress.Permissions
@using ShaheerExpress.Pages.Authors
@using ShaheerExpress.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using ShaheerExpress.Localization
@inject IHtmlLocalizer<ShaheerExpressResource> L
@inject IAuthorizationService Authorization
@model ShaheerExpress.Pages.Authors.IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Authors"].Value;
    PageLayout.Content.MenuItemName = ShaheerExpressMenus.Authors;
}
@section styles
{

}

@section scripts
{
    @* <abp-script src="/libs/vue/vue.global.js" /> *@
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <abp-script src="/Pages/Authors/index.js" />
}
@section content_toolbar {

}
<div id="authorsApp">

    <abp-card>
        <abp-card-header>
            <abp-row>
                <abp-column size-md="_6">
                    <abp-card-title>@L["Authors"]</abp-card-title>
                </abp-column>
                <abp-column size-md="_6" class="text-end">
                    @if (await Authorization.IsGrantedAsync(ShaheerExpressPermissions.Authors.Create))
                    {
                        <abp-button id="NewAuthorButton" text="@L["NewAuthor"].Value" icon="plus" size="Small" button-type="Primary" v-on:click="openCreateModal" />
                    }
                </abp-column>
            </abp-row>
        </abp-card-header>
        <abp-card-body>
            <!-- Search and Filters -->
            <abp-row class="mb-3">
                <abp-column size-md="_8" size-lg="_10">
                    <div class="mb-3">
                        <form v-on:submit.prevent="searchAuthors" autocomplete="off">
                            <div class="input-group">
                                <input class="form-control page-search-filter-text"
                                       v-model="filters.filterText"
                                       placeholder="@L["Search"]" />
                                <abp-button button-type="Primary" type="submit" icon="search" />
                            </div>
                        </form>
                    </div>
                </abp-column>
                <abp-column size-md="_4" size-lg="_2">
                    <div class="mb-3">
                        <abp-button style="width:100%"
                                    v-on:click="toggleAdvancedFilters"
                                    button-type="Outline_Primary">
                            @L["Filters"]<i :class="advancedFiltersIcon"></i>
                        </abp-button>
                    </div>
                </abp-column>
            </abp-row>

            <abp-row v-show="showAdvancedFilters" class="mt-3">
                <abp-column size="_3">
                    <div class="mb-3">
                        <label class="form-label">@L["Name"]</label>
                        <input class="form-control"
                               v-model="filters.name"
                               v-on:keypress.enter="searchAuthors"
                               v-on:change="searchAuthors" />
                    </div>
                </abp-column>
                <abp-column size="_3">
                    <div class="mb-3">
                        <label class="form-label">@L["MinBirthdate"]</label>
                        <input type="date"
                               class="form-control"
                               v-model="filters.birthdateMin"
                               v-on:change="searchAuthors" />
                    </div>
                </abp-column>
                <abp-column size="_3">
                    <div class="mb-3">
                        <label class="form-label">@L["MaxBirthdate"]</label>
                        <input type="date"
                               class="form-control"
                               v-model="filters.birthdateMax"
                               v-on:change="searchAuthors" />
                    </div>
                </abp-column>
            </abp-row>

            <!-- Loading state -->
            <div v-if="loading" class="text-center p-4">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <!-- Authors table -->
            <div v-else>
                <table class="table table-striped" v-if="authors.length > 0">
                    <thead>
                        <tr>
                            <th>@L["Actions"]</th>
                            <th v-on:click="sortBy('name')" style="cursor: pointer;">
                                @L["Name"]
                                <i :class="getSortIcon('name')"></i>
                            </th>
                            <th v-on:click="sortBy('birthdate')" style="cursor: pointer;">
                                @L["Birthdate"]
                                <i :class="getSortIcon('birthdate')"></i>
                            </th>
                            <th v-on:click="sortBy('creationTime')" style="cursor: pointer;">
                                @L["CreationTime"]
                                <i :class="getSortIcon('creationTime')"></i>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="author in paginatedAuthors" :key="author.id">
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                            :id="'actions-' + author.id" data-bs-toggle="dropdown">
                                        @L["Actions"]
                                    </button>
                                    <ul class="dropdown-menu" :aria-labelledby="'actions-' + author.id">
                                        <li v-if="canEdit">
                                            <a class="dropdown-item" href="#" v-on:click.prevent="editAuthor(author)">
                                                <i class="fas fa-edit"></i> {{ l('Edit') }}
                                            </a>
                                        </li>
                                        <li v-if="canDelete">
                                            <a class="dropdown-item" href="#" v-on:click.prevent="deleteAuthor(author)">
                                                <i class="fas fa-trash"></i> {{ l('Delete') }}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                            <td>{{ author.name }}</td>
                            <td>{{ formatDate(author.birthdate) }}</td>
                            <td>{{ formatDateTime(author.creationTime) }}</td>
                        </tr>
                    </tbody>
                </table>

                <!-- Empty state -->
                <div v-else class="text-center p-4">
                    <p>No authors found.</p>
                </div>

                <!-- Pagination -->
                <nav v-if="totalPages > 1" aria-label="Authors pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" :class="{ disabled: currentPage === 1 }">
                            <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">Previous</a>
                        </li>
                        <li v-for="page in visiblePages" :key="page"
                            class="page-item" :class="{ active: page === currentPage }">
                            <a class="page-link" href="#" v-on:click.prevent="changePage(page)">{{ page }}</a>
                        </li>
                        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                            <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">Next</a>
                        </li>
                    </ul>
                </nav>

                <!-- Page info -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span class="text-muted">
                            Showing {{ startIndex + 1 }} to {{ Math.min(startIndex + pageSize, totalItems) }} of {{ totalItems }} entries
                        </span>
                    </div>
                    <div>
                        <select v-model="pageSize" v-on:change="changePageSize" class="form-select form-select-sm" style="width: auto;">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
            </div>
        </abp-card-body>
    </abp-card>
</div>