const PostSelection = {
    props: {
        selectedPostId: {
            type: String,
            default: null
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    
    emits: ['post-selected'],
    
    data() {
        return {
            posts: [],
            availablePages: [],
            totalCount: 0,
            currentPage: 1,
            pageSize: 12, // Increased for better live data display
            postsLoading: false,
            pagesLoading: false,
            selectedPost: null,
            l: null,

            // Filters
            filters: {
                facebookPageId: null,
                searchText: ''
            },

            searchTimeout: null,

            // Facebook API pagination
            hasMorePosts: true,
            isLoadingMore: false,

            // Error handling
            apiErrors: [],
            rateLimitExceeded: false
        };
    },
    
    computed: {
        hasPosts() {
            return this.posts && this.posts.length > 0;
        },

        hasPages() {
            return this.availablePages && this.availablePages.length > 0;
        },

        hasApiErrors() {
            return this.apiErrors && this.apiErrors.length > 0;
        }
    },
    
    template: `
        <div class="post-selection">
            <h4 class="mb-4">{{ l('Facebook:SelectPost') || 'Select Post' }}</h4>
            
            <!-- Filters -->
            <div class="form-section mb-4">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">{{ l('Facebook:Page') || 'Page' }}</label>
                        <select class="form-select"
                                v-model="filters.facebookPageId"
                                v-on:change="loadLivePosts"
                                :disabled="pagesLoading || !hasPages">
                            <option :value="null">{{ l('Facebook:AllPages') || 'All Pages' }}</option>
                            <option v-for="page in availablePages"
                                    :key="page.id"
                                    :value="page.id">
                                {{ page.pageName }}
                            </option>
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">{{ l('Facebook:Search') || 'Search' }}</label>
                        <div class="input-group">
                            <input type="text"
                                   class="form-control"
                                   v-model="filters.searchText"
                                   v-on:input="onSearchInput"
                                   v-on:keypress="onSearchKeyPress"
                                   :placeholder="l('Facebook:SearchPosts') || 'Search posts and reels...'"
                                   :disabled="postsLoading">
                            <button class="btn btn-outline-secondary"
                                    type="button"
                                    v-on:click="clearSearch"
                                    v-if="filters.searchText">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="button"
                                class="btn btn-primary d-block w-100"
                                v-on:click="searchPosts"
                                :disabled="postsLoading">
                            <i class="fas fa-search me-1"></i>
                            {{ l('Facebook:Search') || 'Search' }}
                        </button>
                    </div>
                </div>

                <!-- Live Data Notice -->
                <div class="alert alert-info py-2 mb-0">
                    <small>
                        <i class="fas fa-sync-alt me-1"></i>
                        {{ l('Facebook:LiveDataNotice') || 'Showing live posts and reels directly from Facebook. Posts with active campaigns are excluded.' }}
                    </small>
                </div>
            </div>

            <!-- API Errors -->
            <div v-if="hasApiErrors" class="alert alert-warning mb-3">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>{{ l('Facebook:ApiWarnings') || 'API Warnings' }}</h6>
                <ul class="mb-0">
                    <li v-for="error in apiErrors" :key="error">{{ error }}</li>
                </ul>
            </div>

            <!-- Rate Limit Warning -->
            <div v-if="rateLimitExceeded" class="alert alert-warning mb-3">
                <i class="fas fa-clock me-2"></i>
                {{ l('Facebook:RateLimitWarning') || 'Facebook API rate limit exceeded. Some posts may not be displayed. Please try again in a few minutes.' }}
            </div>
            
            <!-- Loading State -->
            <div v-if="postsLoading" class="text-center py-4">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">{{ l('Facebook:LoadingPosts') || 'Loading posts...' }}</p>
            </div>
            
            <!-- No Posts State -->
            <div v-else-if="!hasPosts" class="alert alert-info">
                <div class="text-center p-4">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <h5>{{ l('Facebook:NoPostsAvailable') || 'No Posts Available' }}</h5>
                    <p>{{ l('Facebook:NoLivePostsMessage') || 'No posts or reels available for campaign creation from your Facebook pages. Posts with active campaigns are excluded.' }}</p>
                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" v-on:click="loadLivePosts">
                        <i class="fas fa-sync-alt me-1"></i>
                        {{ l('Facebook:RefreshPosts') || 'Refresh Posts' }}
                    </button>
                </div>
            </div>
            
            <!-- Posts Grid -->
            <div v-else>
                <div class="row">
                    <div v-for="post in posts" 
                         :key="post.id" 
                         class="col-md-6 col-lg-4 mb-3">
                        <div class="post-selection-card" 
                             :class="{ selected: selectedPost && selectedPost.id === post.id }"
                             v-on:click="selectPost(post)">
                            <div class="post-preview">
                                <img v-if="post.pictureUrl" 
                                     :src="post.pictureUrl" 
                                     :alt="'Post thumbnail'"
                                     class="post-thumbnail">
                                <img v-else-if="post.attachmentUrl" 
                                     :src="post.attachmentUrl" 
                                     :alt="'Post attachment'"
                                     class="post-thumbnail">
                                <div v-else class="post-thumbnail-placeholder">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                
                                <div class="post-content">
                                    <div class="post-message" v-if="post.message">
                                        {{ post.message }}
                                    </div>
                                    <div v-else class="text-muted">
                                        [{{ l('Facebook:NoTextContent') || 'No text content' }}]
                                    </div>
                                    
                                    <div class="post-meta mt-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ post.pageName }}</strong>
                                                <br>
                                                <small class="text-muted">{{ formatDate(post.facebookCreatedTime) }}</small>
                                                <br>
                                                <span class="badge" :class="getPostTypeBadgeClass(post.postType)">
                                                    {{ getPostTypeLabel(post.postType) }}
                                                </span>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">
                                                    👍 {{ formatNumber(post.likesCount) }}<br>
                                                    💬 {{ formatNumber(post.commentsCount) }}
                                                    <span v-if="post.sharesCount > 0"><br>🔄 {{ formatNumber(post.sharesCount) }}</span>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Selection Indicator -->
                                    <div v-if="selectedPost && selectedPost.id === post.id" class="selection-indicator">
                                        <i class="fas fa-check-circle text-primary"></i>
                                        <span class="ms-1 text-primary">{{ l('Facebook:Selected') || 'Selected' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Load More Button -->
                <div v-if="hasMorePosts && hasPosts" class="text-center mt-4">
                    <button type="button"
                            class="btn btn-outline-primary"
                            v-on:click="loadMorePosts"
                            :disabled="isLoadingMore">
                        <div v-if="isLoadingMore" class="loading-spinner me-1"></div>
                        <i v-else class="fas fa-plus me-1"></i>
                        {{ isLoadingMore ? (l('Facebook:LoadingMore') || 'Loading More...') : (l('Facebook:LoadMorePosts') || 'Load More Posts') }}
                    </button>
                </div>

                <!-- Posts Count Info -->
                <div v-if="hasPosts" class="text-center mt-3">
                    <small class="text-muted">
                        {{ l('Facebook:ShowingPostsCount', posts.length) || `Showing ${posts.length} posts and reels` }}
                    </small>
                </div>
            </div>
            
            <!-- Selected Post Summary -->
            <div v-if="selectedPost" class="alert alert-success mt-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle me-2"></i>
                    <div>
                        <strong>{{ l('Facebook:PostSelected') || 'Post Selected' }}</strong>
                        <br>
                        <small>{{ selectedPost.pageName }} • {{ formatDate(selectedPost.facebookCreatedTime) }}</small>
                    </div>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        async loadAvailablePages() {
            try {
                this.pagesLoading = true;
                const response = await window.shaheerExpress.services.facebookPage.getList({
                    maxResultCount: 1000,
                    skipCount: 0
                });
                this.availablePages = response.items;
            } catch (error) {
                console.error('Error loading available pages:', error);
                abp.notify.error(this.l('Facebook:ErrorLoadingPages') || 'Error loading Facebook pages');
            } finally {
                this.pagesLoading = false;
            }
        },
        
        async loadLivePosts(resetPosts = true) {
            try {
                this.postsLoading = resetPosts;
                this.apiErrors = [];
                this.rateLimitExceeded = false;

                const params = {
                    limit: this.pageSize,
                    facebookPageId: this.filters.facebookPageId,
                    searchText: this.filters.searchText
                };

                const response = await window.shaheerExpress.services.facebookPost.getLivePostsForCampaignCreation(params);

                if (resetPosts) {
                    this.posts = response;
                } else {
                    // Append new posts (for load more functionality)
                    this.posts = [...this.posts, ...response];
                }

                // Check if we got fewer posts than requested (indicates no more posts)
                this.hasMorePosts = response.length === this.pageSize;

                // If a post was pre-selected, find it in the loaded posts
                if (this.selectedPostId && !this.selectedPost) {
                    const preSelectedPost = this.posts.find(p => p.facebookPostId === this.selectedPostId);
                    if (preSelectedPost) {
                        this.selectPost(preSelectedPost);
                    }
                }
            } catch (error) {
                console.error('Error loading live posts:', error);

                // Handle specific error types
                if (error.message && error.message.includes('429')) {
                    this.rateLimitExceeded = true;
                    this.apiErrors.push(this.l('Facebook:RateLimitError') || 'Facebook API rate limit exceeded. Please try again later.');
                } else if (error.message && (error.message.includes('401') || error.message.includes('403'))) {
                    this.apiErrors.push(this.l('Facebook:AuthenticationError') || 'Facebook authentication expired. Please reconnect your account.');
                } else {
                    this.apiErrors.push(this.l('Facebook:ErrorLoadingLivePosts') || 'Error loading live posts from Facebook. Please try again.');
                }

                abp.notify.error(this.l('Facebook:ErrorLoadingLivePosts') || 'Error loading live posts from Facebook');
            } finally {
                this.postsLoading = false;
            }
        },

        async loadMorePosts() {
            if (this.isLoadingMore || !this.hasMorePosts) return;

            try {
                this.isLoadingMore = true;

                const params = {
                    limit: this.pageSize,
                    facebookPageId: this.filters.facebookPageId,
                    searchText: this.filters.searchText
                };

                const response = await window.shaheerExpress.services.facebookPost.getLivePostsForCampaignCreation(params);

                // Filter out posts we already have
                const existingPostIds = new Set(this.posts.map(p => p.facebookPostId));
                const newPosts = response.filter(p => !existingPostIds.has(p.facebookPostId));

                this.posts = [...this.posts, ...newPosts];
                this.hasMorePosts = newPosts.length === this.pageSize;

            } catch (error) {
                console.error('Error loading more posts:', error);
                abp.notify.error(this.l('Facebook:ErrorLoadingMorePosts') || 'Error loading more posts');
            } finally {
                this.isLoadingMore = false;
            }
        },
        
        selectPost(post) {
            this.selectedPost = post;
            this.$emit('post-selected', post);
        },
        

        
        onSearchInput() {
            // Debounce search input
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            this.searchTimeout = setTimeout(() => {
                this.loadLivePosts(true);
            }, 500);
        },

        onSearchKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                this.searchPosts();
            }
        },

        searchPosts() {
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            this.loadLivePosts(true);
        },

        clearSearch() {
            this.filters.searchText = '';
            this.loadLivePosts(true);
        },
        
        formatNumber(num) {
            if (!num) return '0';
            return num.toLocaleString();
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            });
        },

        getPostTypeLabel(postType) {
            switch (postType?.toLowerCase()) {
                case 'photo':
                    return this.l('Facebook:Photo') || 'Photo';
                case 'video':
                    return this.l('Facebook:Video') || 'Video';
                case 'video_reel':
                    return this.l('Facebook:Reel') || 'Reel';
                case 'link':
                    return this.l('Facebook:Link') || 'Link';
                case 'status':
                    return this.l('Facebook:Status') || 'Status';
                case 'album':
                    return this.l('Facebook:Album') || 'Album';
                default:
                    return postType || this.l('Facebook:Post') || 'Post';
            }
        },

        getPostTypeBadgeClass(postType) {
            switch (postType?.toLowerCase()) {
                case 'photo':
                case 'album':
                    return 'badge-primary';
                case 'video':
                    return 'badge-info';
                case 'video_reel':
                    return 'badge-success';
                case 'link':
                    return 'badge-warning';
                case 'status':
                    return 'badge-secondary';
                default:
                    return 'badge-light';
            }
        },
        
        // Public method to get selected post
        getSelectedPost() {
            return this.selectedPost;
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },
    async mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');

        // Load initial data
        await this.loadAvailablePages();
        await this.loadLivePosts();
    },
    
    beforeUnmount() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
};
