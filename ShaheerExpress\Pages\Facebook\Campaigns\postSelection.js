const PostSelection = {
    props: {
        selectedPostId: {
            type: String,
            default: null
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    
    emits: ['post-selected'],
    
    data() {
        return {
            posts: [],
            availablePages: [],
            totalCount: 0,
            currentPage: 1,
            pageSize: 6,
            postsLoading: false,
            pagesLoading: false,
            selectedPost: null,
            l: null,
            
            // Filters
            filters: {
                facebookPageId: null,
                hasActiveCampaign: false, // Only show posts without active campaigns
                searchText: '',
                fromDate: null,
                toDate: null
            },
            
            searchTimeout: null
        };
    },
    
    computed: {
        totalPages() {
            return Math.ceil(this.totalCount / this.pageSize);
        },
        
        hasPosts() {
            return this.posts && this.posts.length > 0;
        },
        
        hasPages() {
            return this.availablePages && this.availablePages.length > 0;
        }
    },
    
    template: `
        <div class="post-selection">
            <h4 class="mb-4">{{ l('Facebook:SelectPost') || 'Select Post' }}</h4>
            
            <!-- Filters -->
            <div class="form-section mb-4">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">{{ l('Facebook:Page') || 'Page' }}</label>
                        <select class="form-select" 
                                v-model="filters.facebookPageId" 
                                v-on:change="loadPosts"
                                :disabled="pagesLoading || !hasPages">
                            <option :value="null">{{ l('Facebook:AllPages') || 'All Pages' }}</option>
                            <option v-for="page in availablePages" 
                                    :key="page.id" 
                                    :value="page.id">
                                {{ page.pageName }}
                            </option>
                        </select>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label">{{ l('Facebook:Search') || 'Search' }}</label>
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   v-model="filters.searchText"
                                   v-on:input="onSearchInput"
                                   v-on:keypress="onSearchKeyPress"
                                   :placeholder="l('Facebook:SearchPosts') || 'Search posts...'"
                                   :disabled="postsLoading">
                            <button class="btn btn-outline-secondary" 
                                    type="button" 
                                    v-on:click="clearSearch"
                                    v-if="filters.searchText">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" 
                                class="btn btn-primary d-block w-100" 
                                v-on:click="searchPosts"
                                :disabled="postsLoading">
                            <i class="fas fa-search me-1"></i>
                            {{ l('Facebook:Search') || 'Search' }}
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Loading State -->
            <div v-if="postsLoading" class="text-center py-4">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">{{ l('Facebook:LoadingPosts') || 'Loading posts...' }}</p>
            </div>
            
            <!-- No Posts State -->
            <div v-else-if="!hasPosts" class="alert alert-info">
                <div class="text-center p-4">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <h5>{{ l('Facebook:NoPostsAvailable') || 'No Posts Available' }}</h5>
                    <p>{{ l('Facebook:NoPostsForCampaignMessage') || 'No posts available for campaign creation. Posts with active campaigns are not shown.' }}</p>
                </div>
            </div>
            
            <!-- Posts Grid -->
            <div v-else>
                <div class="row">
                    <div v-for="post in posts" 
                         :key="post.id" 
                         class="col-md-6 col-lg-4 mb-3">
                        <div class="post-selection-card" 
                             :class="{ selected: selectedPost && selectedPost.id === post.id }"
                             v-on:click="selectPost(post)">
                            <div class="post-preview">
                                <img v-if="post.pictureUrl" 
                                     :src="post.pictureUrl" 
                                     :alt="'Post thumbnail'"
                                     class="post-thumbnail">
                                <img v-else-if="post.attachmentUrl" 
                                     :src="post.attachmentUrl" 
                                     :alt="'Post attachment'"
                                     class="post-thumbnail">
                                <div v-else class="post-thumbnail-placeholder">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                
                                <div class="post-content">
                                    <div class="post-message" v-if="post.message">
                                        {{ post.message }}
                                    </div>
                                    <div v-else class="text-muted">
                                        [{{ l('Facebook:NoTextContent') || 'No text content' }}]
                                    </div>
                                    
                                    <div class="post-meta mt-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ post.pageName }}</strong>
                                                <br>
                                                <small class="text-muted">{{ formatDate(post.facebookCreatedTime) }}</small>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">
                                                    👍 {{ formatNumber(post.likesCount) }}<br>
                                                    💬 {{ formatNumber(post.commentsCount) }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Selection Indicator -->
                                    <div v-if="selectedPost && selectedPost.id === post.id" class="selection-indicator">
                                        <i class="fas fa-check-circle text-primary"></i>
                                        <span class="ms-1 text-primary">{{ l('Facebook:Selected') || 'Selected' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div v-if="totalPages > 1" class="d-flex justify-content-center mt-4">
                    <nav>
                        <ul class="pagination">
                            <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">
                                    {{ l('Previous') || 'Previous' }}
                                </a>
                            </li>
                            
                            <li v-for="page in getVisiblePages()" 
                                :key="page" 
                                class="page-item" 
                                :class="{ active: page === currentPage }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(page)">
                                    {{ page }}
                                </a>
                            </li>
                            
                            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">
                                    {{ l('Next') || 'Next' }}
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
            
            <!-- Selected Post Summary -->
            <div v-if="selectedPost" class="alert alert-success mt-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle me-2"></i>
                    <div>
                        <strong>{{ l('Facebook:PostSelected') || 'Post Selected' }}</strong>
                        <br>
                        <small>{{ selectedPost.pageName }} • {{ formatDate(selectedPost.facebookCreatedTime) }}</small>
                    </div>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        async loadAvailablePages() {
            try {
                this.pagesLoading = true;
                const response = await window.shaheerExpress.services.facebookPage.getList({
                    maxResultCount: 1000,
                    skipCount: 0
                });
                this.availablePages = response.items;
            } catch (error) {
                console.error('Error loading available pages:', error);
                abp.notify.error(this.l('Facebook:ErrorLoadingPages') || 'Error loading Facebook pages');
            } finally {
                this.pagesLoading = false;
            }
        },
        
        async loadPosts() {
            try {
                this.postsLoading = true;
                
                const params = {
                    skipCount: (this.currentPage - 1) * this.pageSize,
                    maxResultCount: this.pageSize,
                    sorting: 'facebookCreatedTime desc',
                    facebookPageId: this.filters.facebookPageId,
                    hasActiveCampaign: this.filters.hasActiveCampaign,
                    searchText: this.filters.searchText,
                    fromDate: this.filters.fromDate,
                    toDate: this.filters.toDate
                };
                
                const response = await window.shaheerExpress.services.facebookPost.getList(params);
                this.posts = response.items;
                this.totalCount = response.totalCount;
                
                // If a post was pre-selected, find it in the loaded posts
                if (this.selectedPostId && !this.selectedPost) {
                    const preSelectedPost = this.posts.find(p => p.id === this.selectedPostId);
                    if (preSelectedPost) {
                        this.selectPost(preSelectedPost);
                    }
                }
            } catch (error) {
                console.error('Error loading posts:', error);
                abp.notify.error(this.l('Facebook:ErrorLoadingPosts') || 'Error loading Facebook posts');
            } finally {
                this.postsLoading = false;
            }
        },
        
        selectPost(post) {
            this.selectedPost = post;
            this.$emit('post-selected', post);
        },
        
        changePage(page) {
            if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                this.currentPage = page;
                this.loadPosts();
            }
        },
        
        getVisiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        },
        
        onSearchInput() {
            // Debounce search input
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            
            this.searchTimeout = setTimeout(() => {
                this.currentPage = 1;
                this.loadPosts();
            }, 500);
        },
        
        onSearchKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                this.searchPosts();
            }
        },
        
        searchPosts() {
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            this.currentPage = 1;
            this.loadPosts();
        },
        
        clearSearch() {
            this.filters.searchText = '';
            this.currentPage = 1;
            this.loadPosts();
        },
        
        formatNumber(num) {
            if (!num) return '0';
            return num.toLocaleString();
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric' 
            });
        },
        
        // Public method to get selected post
        getSelectedPost() {
            return this.selectedPost;
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },
    async mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
        
        // Load initial data
        await this.loadAvailablePages();
        await this.loadPosts();
    },
    
    beforeUnmount() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
};
