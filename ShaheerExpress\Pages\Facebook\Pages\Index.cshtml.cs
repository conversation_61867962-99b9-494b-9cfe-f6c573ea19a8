using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ShaheerExpress.Services;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace ShaheerExpress.Pages.Facebook.Pages
{
    public class IndexModel : AbpPageModel
    {
        protected IFacebookPageService _facebookPageService;
        protected IFacebookAuthService _facebookAuthService;

        public IndexModel(
            IFacebookPageService facebookPageService,
            IFacebookAuthService facebookAuthService)
        {
            _facebookPageService = facebookPageService;
            _facebookAuthService = facebookAuthService;
        }

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
