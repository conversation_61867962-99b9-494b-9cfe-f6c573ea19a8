const FacebookPageList = {
    props: {
        pages: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        totalCount: {
            type: Number,
            default: 0
        },
        currentPage: {
            type: Number,
            default: 1
        },
        pageSize: {
            type: Number,
            default: 10
        },
        canManage: {
            type: Boolean,
            default: false
        },
        pageTokenStatuses: {
            type: Object,
            default: () => ({})
        }
    },
    
    emits: [
        'page-changed',
        'sync-page',
        'disconnect-page',
        'reconnect-page',
        'subscribe-webhook',
        'unsubscribe-webhook',
        'create-campaign'
    ],
    
    data() {
        return {
            l: null
        };
    },
    
    computed: {
        totalPages() {
            return Math.ceil(this.totalCount / this.pageSize);
        },
        
        hasPages() {
            return this.pages && this.pages.length > 0;
        }
    },
    
    template: `
        <div class="facebook-page-list">
            <!-- Loading State -->
            <div v-if="loading" class="text-center py-4">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">{{ l('Facebook:LoadingPages') || 'Loading Facebook Pages...' }}</p>
            </div>
            
            <!-- No Pages State -->
            <div v-else-if="!hasPages" class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                {{ l('Facebook:NoPagesFound') || 'No Facebook Pages found. Click "Import Pages" to import your Facebook Pages.' }}
            </div>
            
            <!-- Pages Table -->
            <div v-else class="facebook-page-card">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 80px;"></th>
                                <th>{{ l('Facebook:PageName') || 'Page Name' }}</th>
                                <th>{{ l('Facebook:Followers') || 'Followers' }}</th>
                                <th>{{ l('Facebook:Status') || 'Status' }}</th>
                                <th>{{ l('Facebook:LastSync') || 'Last Sync' }}</th>
                                <th v-if="canManage" style="width: 200px;">{{ l('Actions') || 'Actions' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="page in pages" :key="page.id">
                                <!-- Page Avatar -->
                                <td>
                                    <img v-if="page.pageProfilePictureUrl" 
                                         :src="page.pageProfilePictureUrl" 
                                         :alt="page.pageName"
                                         class="page-avatar">
                                    <i v-else class="fas fa-file-alt text-muted" style="font-size: 24px;"></i>
                                </td>
                                
                                <!-- Page Name and Category -->
                                <td>
                                    <div>
                                        <strong>{{ page.pageName }}</strong>
                                        <br>
                                        <small class="text-muted">{{ page.category || 'No category' }}</small>
                                    </div>
                                </td>
                                
                                <!-- Followers Count -->
                                <td>
                                    {{ formatNumber(page.followersCount) }}
                                </td>
                                
                                <!-- Status -->
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        <div>
                                            <span v-if="page.isConnected" class="page-status-badge status-connected">
                                                {{ l('Facebook:Connected') || 'Connected' }}
                                            </span>
                                            <span v-else class="page-status-badge status-disconnected">
                                                {{ l('Facebook:Disconnected') || 'Disconnected' }}
                                            </span>
                                            <span v-if="page.webhookSubscribed" class="webhook-indicator webhook-active ms-1">
                                                {{ l('Facebook:WebhookActive') || 'Webhook Active' }}
                                            </span>
                                        </div>
                                        
                                        <!-- Token Status -->
                                        <div v-if="page.isConnected && pageTokenStatuses[page.id]">
                                            <span class="token-status-indicator" 
                                                  :class="getTokenStatusClass(page.id)"></span>
                                            <small :class="getTokenStatusTextClass(page.id)">
                                                {{ getTokenStatusText(page.id) }}
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                
                                <!-- Last Sync -->
                                <td>
                                    <span v-if="page.lastSyncAt">
                                        {{ formatDateTime(page.lastSyncAt) }}
                                    </span>
                                    <span v-else class="text-muted">
                                        {{ l('Never') || 'Never' }}
                                    </span>
                                </td>
                                
                                <!-- Actions -->
                                <td v-if="canManage">
                                    <div class="dropdown">
                                        <button class="btn btn-primary btn-sm dropdown-toggle" 
                                                type="button" 
                                                :id="'pageActions' + page.id"
                                                data-bs-toggle="dropdown" 
                                                aria-expanded="false">
                                            {{ l('Actions') || 'Actions' }}
                                        </button>
                                        <ul class="dropdown-menu page-actions-dropdown" :aria-labelledby="'pageActions' + page.id">
                                            <li>
                                                <a class="dropdown-item" href="#" v-on:click.prevent="$emit('sync-page', page.id)">
                                                    <i class="fas fa-sync-alt me-2"></i>
                                                    {{ l('Facebook:Sync') || 'Sync' }}
                                                </a>
                                            </li>
                                            
                                            <li v-if="page.isConnected && !page.webhookSubscribed">
                                                <a class="dropdown-item" href="#" v-on:click.prevent="$emit('subscribe-webhook', page.id)">
                                                    <i class="fas fa-bell me-2"></i>
                                                    {{ l('Facebook:EnableWebhook') || 'Enable Webhook' }}
                                                </a>
                                            </li>
                                            
                                            <li v-if="page.webhookSubscribed">
                                                <a class="dropdown-item" href="#" v-on:click.prevent="$emit('unsubscribe-webhook', page.id)">
                                                    <i class="fas fa-bell-slash me-2"></i>
                                                    {{ l('Facebook:DisableWebhook') || 'Disable Webhook' }}
                                                </a>
                                            </li>
                                            
                                            <li><hr class="dropdown-divider"></li>
                                            
                                            <li v-if="page.isConnected">
                                                <a class="dropdown-item" href="#" v-on:click.prevent="$emit('disconnect-page', page.id)">
                                                    <i class="fas fa-times me-2"></i>
                                                    {{ l('Facebook:Disconnect') || 'Disconnect' }}
                                                </a>
                                            </li>
                                            
                                            <li v-else>
                                                <a class="dropdown-item" href="#" v-on:click.prevent="$emit('reconnect-page', page.id)">
                                                    <i class="fas fa-link me-2"></i>
                                                    {{ l('Facebook:Reconnect') || 'Reconnect' }}
                                                </a>
                                            </li>
                                            
                                            <li><hr class="dropdown-divider"></li>
                                            
                                            <li>
                                                <a class="dropdown-item text-primary" href="#" v-on:click.prevent="$emit('create-campaign', page.id)">
                                                    <i class="fas fa-cogs me-2"></i>
                                                    {{ l('Facebook:CreateCampaign') || 'Create Auto-Reply Campaign' }}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center p-3 border-top">
                    <div class="text-muted">
                        {{ l('Facebook:ShowingPages', (currentPage - 1) * pageSize + 1, Math.min(currentPage * pageSize, totalCount), totalCount) }}
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">
                                    {{ l('Previous') || 'Previous' }}
                                </a>
                            </li>
                            
                            <li v-for="page in getVisiblePages()" 
                                :key="page" 
                                class="page-item" 
                                :class="{ active: page === currentPage }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(page)">
                                    {{ page }}
                                </a>
                            </li>
                            
                            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">
                                    {{ l('Next') || 'Next' }}
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        changePage(page) {
            if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                this.$emit('page-changed', page);
            }
        },
        
        getVisiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        },
        
        getTokenStatusClass(pageId) {
            const status = this.pageTokenStatuses[pageId];
            if (!status) return 'token-warning';
            
            if (status.isTokenValid && !status.requiresReconnection) {
                return 'token-valid';
            } else if (status.isTokenExpired) {
                return 'token-expired';
            } else {
                return 'token-warning';
            }
        },
        
        getTokenStatusTextClass(pageId) {
            const status = this.pageTokenStatuses[pageId];
            if (!status) return 'text-warning';
            
            if (status.isTokenValid && !status.requiresReconnection) {
                return 'text-success';
            } else if (status.isTokenExpired) {
                return 'text-danger';
            } else {
                return 'text-warning';
            }
        },
        
        getTokenStatusText(pageId) {
            const status = this.pageTokenStatuses[pageId];
            if (!status) return this.l('Facebook:TokenUnknown') || 'Token status unknown';
            
            if (status.isTokenValid && !status.requiresReconnection) {
                return this.l('Facebook:TokenValid') || 'Token valid';
            } else if (status.isTokenExpired) {
                return this.l('Facebook:TokenExpired') || 'Token expired';
            } else if (status.requiresReconnection) {
                return this.l('Facebook:RequiresReconnection') || 'Requires reconnection';
            } else {
                return this.l('Facebook:TokenIssue') || 'Token issue';
            }
        },
        
        formatNumber(num) {
            if (!num) return '0';
            return num.toLocaleString();
        },
        
        formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString();
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },
    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    }
};
