function initializeVueApp() {
    if (typeof Vue === 'undefined') {
        setTimeout(initializeVueApp, 100);
        return;
    }
    else
        createVueApp();
}

function createVueApp() {
    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                posts: [],
                availablePages: [],
                totalCount: 0,
                currentPage: 1,
                pageSize: 10,
                currentSorting: 'facebookCreatedTime desc',
                loading: false,
                postsLoading: false,
                filtersLoading: false,
                loadingMessage: 'Loading...',
                isConnected: false,
                l: null,
                
                // Filters
                filters: {
                    facebookPageId: null,
                    hasActiveCampaign: null,
                    searchText: '',
                    fromDate: null,
                    toDate: null
                },
                
                // Sync progress
                syncProgress: {
                    isVisible: false,
                    title: '',
                    message: ''
                },
                
                // Permissions
                canSyncPosts: false,
                canCreateCampaign: false,
                canViewPosts: false
            };
        },
        
        methods: {
            async loadInitialData() {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Loading Facebook connection status...';
                    
                    // Check if user is connected to Facebook
                    this.isConnected = await window.shaheerExpress.services.facebookAuth.isConnectedToFacebook();
                    
                    if (this.isConnected) {
                        await Promise.all([
                            this.loadAvailablePages(),
                            this.loadPosts()
                        ]);
                    }
                } catch (error) {
                    console.error('Error loading initial data:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingData') || 'Error loading data');
                } finally {
                    this.loading = false;
                }
            },
            
            async loadAvailablePages() {
                try {
                    this.filtersLoading = true;
                    const response = await window.shaheerExpress.services.facebookPage.getList({
                        maxResultCount: 1000,
                        skipCount: 0
                    });
                    this.availablePages = response.items;
                } catch (error) {
                    console.error('Error loading available pages:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingPages') || 'Error loading Facebook pages');
                } finally {
                    this.filtersLoading = false;
                }
            },
            
            async loadPosts() {
                try {
                    this.postsLoading = true;
                    
                    const params = {
                        skipCount: (this.currentPage - 1) * this.pageSize,
                        maxResultCount: this.pageSize,
                        sorting: this.currentSorting,
                        facebookPageId: this.filters.facebookPageId,
                        hasActiveCampaign: this.filters.hasActiveCampaign,
                        searchText: this.filters.searchText,
                        fromDate: this.filters.fromDate,
                        toDate: this.filters.toDate
                    };
                    
                    const response = await window.shaheerExpress.services.facebookPost.getList(params);
                    this.posts = response.items;
                    this.totalCount = response.totalCount;
                } catch (error) {
                    console.error('Error loading posts:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingPosts') || 'Error loading Facebook posts');
                } finally {
                    this.postsLoading = false;
                }
            },
            
            async refreshPosts() {
                await this.loadPosts();
            },
            
            async syncAllPosts() {
                if (!this.canSyncPosts) return;
                
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:SyncAllPostsConfirmation') || 'Are you sure you want to sync all posts? This may take several minutes.',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.syncProgress.isVisible = true;
                        this.syncProgress.title = this.l('Facebook:SyncingAllPosts') || 'Syncing All Posts';
                        this.syncProgress.message = this.l('Facebook:SyncingPostsMessage') || 'This may take several minutes...';
                        
                        await window.shaheerExpress.services.facebookPost.syncAllPosts();
                        abp.notify.success(this.l('Facebook:SyncAllPostsSuccess') || 'All posts synced successfully');
                        
                        // Reload posts
                        await this.loadPosts();
                    } catch (error) {
                        console.error('Error syncing all posts:', error);
                        abp.notify.error(this.l('Facebook:ErrorSyncingAllPosts') || 'Error syncing posts');
                    } finally {
                        this.syncProgress.isVisible = false;
                    }
                }
            },
            
            navigateToConnection() {
                window.location.href = '/Facebook/Connection';
            },
            
            handleFiltersChanged(newFilters) {
                this.filters = { ...newFilters };
                this.currentPage = 1; // Reset to first page when filters change
                this.loadPosts();
            },
            
            handlePageChanged(page) {
                this.currentPage = page;
                this.loadPosts();
            },
            
            handleSortChanged(sorting) {
                this.currentSorting = sorting;
                this.currentPage = 1; // Reset to first page when sorting changes
                this.loadPosts();
            },
            
            handleViewOnFacebook(permalinkUrl) {
                if (permalinkUrl) {
                    window.open(permalinkUrl, '_blank');
                }
            },
            
            handleCreateCampaign(postId) {
                // Navigate to campaign creation page with post ID
                window.location.href = `/Campaigns/Create?postId=${postId}`;
            },
            
            handleViewCampaign(postId) {
                // Navigate to campaign view page
                window.location.href = `/Campaigns?postId=${postId}`;
            },
            
            async handleSyncPost(postId) {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Syncing post...';
                    
                    // Find the post to get the page ID
                    const post = this.posts.find(p => p.id === postId);
                    if (post) {
                        await window.shaheerExpress.services.facebookPost.syncPostsFromPage(post.facebookPageId);
                        abp.notify.success(this.l('Facebook:PostSyncSuccess') || 'Post synced successfully');
                        
                        // Reload posts
                        await this.loadPosts();
                    }
                } catch (error) {
                    console.error('Error syncing post:', error);
                    abp.notify.error(this.l('Facebook:ErrorSyncingPost') || 'Error syncing post');
                } finally {
                    this.loading = false;
                }
            },
            
            // URL parameter handling
            checkUrlParameters() {
                const urlParams = new URLSearchParams(window.location.search);
                
                // Check for page ID parameter
                if (urlParams.has('pageId')) {
                    const pageId = urlParams.get('pageId');
                    if (pageId) {
                        this.filters.facebookPageId = pageId;
                    }
                }
                
                // Check for campaign filter parameter
                if (urlParams.has('hasActiveCampaign')) {
                    const hasActiveCampaign = urlParams.get('hasActiveCampaign');
                    if (hasActiveCampaign === 'true') {
                        this.filters.hasActiveCampaign = true;
                    } else if (hasActiveCampaign === 'false') {
                        this.filters.hasActiveCampaign = false;
                    }
                }
            }
        },
        
        async mounted() {
            // Initialize ABP localization
            this.l = abp.localization.getResource('ShaheerExpress');
            
            // Check permissions
            this.canViewPosts = abp.auth.isGranted('ShaheerExpress.Posts.View');
            this.canSyncPosts = abp.auth.isGranted('ShaheerExpress.Posts.Sync');
            this.canCreateCampaign = abp.auth.isGranted('ShaheerExpress.Campaigns.Create');
            
            // Check URL parameters
            this.checkUrlParameters();
            
            // Load initial data
            await this.loadInitialData();
        }
    });
    
    // Register components (will be defined in separate files)
    app.component('facebook-post-list', FacebookPostList);
    app.component('facebook-post-filters', FacebookPostFilters);
    app.component('facebook-post-actions', FacebookPostActions);
    
    app.mount('#facebookPostsApp');
}

// Initialize the Vue app when DOM is ready
document.addEventListener('DOMContentLoaded', initializeVueApp);
