﻿using ShaheerExpress.Authors;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace ShaheerExpress.Services.Authors
{
    public interface IAuthorsAppService : IApplicationService
    {

        Task<PagedResultDto<AuthorDto>> GetListAsync(GetAuthorsInput input);

        Task<AuthorDto> GetAsync(Guid id);

        Task DeleteAsync(Guid id);

        Task<AuthorDto> CreateAsync(AuthorCreateDto input);

        Task<AuthorDto> UpdateAsync(Guid id, AuthorUpdateDto input);
    }
}
