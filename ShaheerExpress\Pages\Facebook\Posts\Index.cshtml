@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using ShaheerExpress.Permissions
@using ShaheerExpress.Pages.Facebook.Posts
@using ShaheerExpress.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using ShaheerExpress.Localization
@inject IHtmlLocalizer<ShaheerExpressResource> L
@inject IAuthorizationService Authorization
@model ShaheerExpress.Pages.Facebook.Posts.IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Facebook:Posts"].Value;
    PageLayout.Content.MenuItemName = ShaheerExpressMenus.FacebookPosts;
}

@section styles
{
    <style>
        .facebook-post-card {
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .post-thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .post-thumbnail-placeholder {
            width: 60px;
            height: 60px;
            background-color: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
        
        .post-content {
            max-width: 300px;
        }
        
        .post-message {
            margin-bottom: 0.25rem;
        }
        
        .post-message.truncated {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .engagement-stats {
            font-size: 0.875rem;
        }
        
        .engagement-stats .stat-item {
            display: inline-block;
            margin-right: 0.75rem;
        }
        
        .campaign-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        
        .campaign-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .campaign-none {
            background-color: #e2e3e5;
            color: #495057;
            border: 1px solid #ced4da;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .filters-card {
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .post-actions-dropdown {
            min-width: 160px;
        }
        
        .post-type-indicator {
            font-size: 0.75rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            background-color: #e9ecef;
            color: #495057;
        }
        
        .post-type-photo {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .post-type-video {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .post-type-link {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .sync-progress {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
    </style>
}

@section scripts
{
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <abp-script src="/Pages/Facebook/Posts/facebookPostList.js" />
    <abp-script src="/Pages/Facebook/Posts/facebookPostFilters.js" />
    <abp-script src="/Pages/Facebook/Posts/facebookPostActions.js" />
    <abp-script src="/Pages/Facebook/Posts/index.js" />
}

@section content_toolbar {

}

<div id="facebookPostsApp">
    <!-- Loading State -->
    <div v-if="loading" class="text-center py-4">
        <div class="loading-spinner"></div>
        <p class="mt-2 text-muted">{{ loadingMessage }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fas fa-comments text-primary me-2"></i>
                            @L["Facebook:Posts"]
                        </h2>
                        <p class="text-muted mb-0">@L["Facebook:PostsDescription"]</p>
                    </div>
                    <div id="facebookPostsToolbar" v-if="isConnected">
                        <div class="d-flex gap-2">
                            <button type="button"
                                    class="btn btn-outline-info btn-sm"
                                    v-on:click="refreshPosts"
                                    :disabled="loading">
                                <div v-if="loading" class="loading-spinner me-1"></div>
                                <i v-else class="fas fa-sync-alt me-1"></i>
                                @L["Facebook:Refresh"]
                            </button>
                            <button type="button"
                                    class="btn btn-secondary btn-sm"
                                    v-on:click="syncAllPosts"
                                    v-if="canSyncPosts">
                                <i class="fas fa-sync-alt me-1"></i>
                                @L["Facebook:SyncAllPosts"]
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Connection Required -->
        <div v-if="!isConnected" class="alert alert-warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>@L["Facebook:ConnectionRequired"]</strong>
                    <br>
                    <span>@L["Facebook:PostsConnectionRequiredMessage"]</span>
                </div>
                <button type="button" class="btn btn-primary btn-sm" v-on:click="navigateToConnection">
                    <i class="fas fa-link me-1"></i>
                    @L["Facebook:ConnectNow"]
                </button>
            </div>
        </div>

        <!-- Filters Component -->
        <facebook-post-filters 
            v-if="isConnected"
            ref="filtersComponent"
            :pages="availablePages"
            :loading="filtersLoading"
            v-on:filters-changed="handleFiltersChanged">
        </facebook-post-filters>

        <!-- Sync Progress -->
        <div v-if="syncProgress.isVisible" class="sync-progress">
            <div class="d-flex align-items-center">
                <div class="loading-spinner me-2"></div>
                <div>
                    <strong>{{ syncProgress.title }}</strong>
                    <br>
                    <small class="text-muted">{{ syncProgress.message }}</small>
                </div>
            </div>
        </div>

        <!-- Facebook Post List Component -->
        <facebook-post-list 
            v-if="isConnected"
            ref="postListComponent"
            :posts="posts"
            :loading="postsLoading"
            :total-count="totalCount"
            :current-page="currentPage"
            :page-size="pageSize"
            :can-sync="canSyncPosts"
            :can-create-campaign="canCreateCampaign"
            v-on:page-changed="handlePageChanged"
            v-on:sort-changed="handleSortChanged"
            v-on:view-on-facebook="handleViewOnFacebook"
            v-on:create-campaign="handleCreateCampaign"
            v-on:view-campaign="handleViewCampaign"
            v-on:sync-post="handleSyncPost">
        </facebook-post-list>
    </div>
</div>
