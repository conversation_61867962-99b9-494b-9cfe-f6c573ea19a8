@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using ShaheerExpress.Permissions
@using ShaheerExpress.Pages.Facebook.Campaigns
@using ShaheerExpress.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using ShaheerExpress.Localization
@inject IHtmlLocalizer<ShaheerExpressResource> L
@inject IAuthorizationService Authorization
@model ShaheerExpress.Pages.Facebook.Campaigns.IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Facebook:Campaigns"].Value;
    PageLayout.Content.MenuItemName = ShaheerExpressMenus.FacebookCampaigns;
}

@section styles
{
    <style>
        .facebook-campaign-card {
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .campaign-status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-ended {
            background-color: #e2e3e5;
            color: #495057;
            border: 1px solid #ced4da;
        }
        
        .campaign-metrics {
            display: flex;
            gap: 1rem;
            font-size: 0.875rem;
        }
        
        .metric-item {
            text-align: center;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 1.1rem;
            color: #007bff;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.75rem;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .filters-card {
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .campaign-actions-dropdown {
            min-width: 180px;
        }
        
        .campaign-post-preview {
            max-width: 250px;
            font-size: 0.875rem;
        }
        
        .campaign-post-message {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .reply-type-indicator {
            font-size: 0.75rem;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            background-color: #e9ecef;
            color: #495057;
            margin-right: 0.25rem;
        }
        
        .reply-type-public {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .reply-type-private {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .campaign-performance-summary {
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
}

@section scripts
{
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <abp-script src="/Pages/Facebook/Campaigns/campaignList.js" />
    <abp-script src="/Pages/Facebook/Campaigns/campaignFilters.js" />
    <abp-script src="/Pages/Facebook/Campaigns/campaignActions.js" />
    <abp-script src="/Pages/Facebook/Campaigns/index.js" />
}

@section content_toolbar {

}

<div id="facebookCampaignsApp">
    <!-- Loading State -->
    <div v-if="loading" class="text-center py-4">
        <div class="loading-spinner"></div>
        <p class="mt-2 text-muted">{{ loadingMessage }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fas fa-cogs text-primary me-2"></i>
                            @L["Facebook:Campaigns"]
                        </h2>
                        <p class="text-muted mb-0">@L["Facebook:CampaignsDescription"]</p>
                    </div>
                    <div id="facebookCampaignsToolbar" v-if="isConnected">
                        <div class="d-flex gap-2">
                            <button type="button"
                                    class="btn btn-outline-info btn-sm"
                                    v-on:click="refreshCampaigns"
                                    :disabled="loading">
                                <div v-if="loading" class="loading-spinner me-1"></div>
                                <i v-else class="fas fa-sync-alt me-1"></i>
                                @L["Facebook:Refresh"]
                            </button>
                            <button type="button"
                                    class="btn btn-primary btn-sm"
                                    v-on:click="navigateToCreateCampaign"
                                    v-if="canCreateCampaign">
                                <i class="fas fa-plus me-1"></i>
                                @L["Facebook:CreateCampaign"]
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Connection Required -->
        <div v-if="!isConnected" class="alert alert-warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>@L["Facebook:ConnectionRequired"]</strong>
                    <br>
                    <span>@L["Facebook:CampaignsConnectionRequiredMessage"]</span>
                </div>
                <button type="button" class="btn btn-primary btn-sm" v-on:click="navigateToConnection">
                    <i class="fas fa-link me-1"></i>
                    @L["Facebook:ConnectNow"]
                </button>
            </div>
        </div>

        <!-- Performance Summary -->
        <div v-if="isConnected && performanceSummary" class="campaign-performance-summary">
            <div class="row">
                <div class="col-12">
                    <h6 class="mb-3">
                        <i class="fas fa-chart-line me-2"></i>
                        @L["Facebook:CampaignPerformanceSummary"]
                    </h6>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="metric-item">
                        <div class="metric-value">{{ performanceSummary.totalCampaigns }}</div>
                        <div class="metric-label">@L["Facebook:TotalCampaigns"]</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="metric-item">
                        <div class="metric-value">{{ performanceSummary.activeCampaigns }}</div>
                        <div class="metric-label">@L["Facebook:ActiveCampaigns"]</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="metric-item">
                        <div class="metric-value">{{ formatNumber(performanceSummary.totalReplies) }}</div>
                        <div class="metric-label">@L["Facebook:TotalReplies"]</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="metric-item">
                        <div class="metric-value">{{ formatNumber(performanceSummary.totalLikes) }}</div>
                        <div class="metric-label">@L["Facebook:TotalLikes"]</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Component -->
        <campaign-filters 
            v-if="isConnected"
            ref="filtersComponent"
            :pages="availablePages"
            :loading="filtersLoading"
            v-on:filters-changed="handleFiltersChanged">
        </campaign-filters>

        <!-- Campaign List Component -->
        <campaign-list 
            v-if="isConnected"
            ref="campaignListComponent"
            :campaigns="campaigns"
            :loading="campaignsLoading"
            :total-count="totalCount"
            :current-page="currentPage"
            :page-size="pageSize"
            :can-edit="canEditCampaign"
            :can-delete="canDeleteCampaign"
            :can-activate="canActivateCampaign"
            :can-deactivate="canDeactivateCampaign"
            v-on:page-changed="handlePageChanged"
            v-on:sort-changed="handleSortChanged"
            v-on:activate-campaign="handleActivateCampaign"
            v-on:deactivate-campaign="handleDeactivateCampaign"
            v-on:edit-campaign="handleEditCampaign"
            v-on:delete-campaign="handleDeleteCampaign"
            v-on:view-details="handleViewDetails"
            v-on:view-activities="handleViewActivities">
        </campaign-list>
    </div>
</div>
