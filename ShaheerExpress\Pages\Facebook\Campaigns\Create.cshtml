@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using ShaheerExpress.Permissions
@using ShaheerExpress.Pages.Facebook.Campaigns
@using ShaheerExpress.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using ShaheerExpress.Localization
@inject IHtmlLocalizer<ShaheerExpressResource> L
@inject IAuthorizationService Authorization
@model ShaheerExpress.Pages.Facebook.Campaigns.CreateModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Facebook:CreateCampaign"].Value;
    PageLayout.Content.BreadCrumb.Add(L["Facebook:Campaigns"].Value, "/Facebook/Campaigns");
}

@section styles
{
    <style>
        .campaign-creation-wizard {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .wizard-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fc;
            border-radius: 0.5rem 0.5rem 0 0;
        }
        
        .wizard-step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .wizard-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 15px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #dee2e6;
            z-index: 1;
        }
        
        .wizard-step.active::after {
            background: #007bff;
        }
        
        .wizard-step.completed::after {
            background: #28a745;
        }
        
        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #dee2e6;
            color: #6c757d;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }
        
        .wizard-step.active .step-circle {
            background: #007bff;
            color: white;
        }
        
        .wizard-step.completed .step-circle {
            background: #28a745;
            color: white;
        }
        
        .step-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6c757d;
        }
        
        .wizard-step.active .step-title {
            color: #007bff;
        }
        
        .wizard-step.completed .step-title {
            color: #28a745;
        }
        
        .wizard-content {
            padding: 2rem;
        }
        
        .post-selection-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .post-selection-card:hover {
            border-color: #007bff;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.25);
        }
        
        .post-selection-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        
        .post-preview {
            display: flex;
            gap: 1rem;
        }
        
        .post-thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 0.375rem;
            object-fit: cover;
        }
        
        .post-thumbnail-placeholder {
            width: 60px;
            height: 60px;
            background: #f8f9fa;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
        
        .post-content {
            flex: 1;
        }
        
        .post-message {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .post-meta {
            font-size: 0.75rem;
            color: #6c757d;
        }
        
        .form-section {
            background: #f8f9fc;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #495057;
        }
        
        .reply-type-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .reply-type-option {
            flex: 1;
            border: 2px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .reply-type-option:hover {
            border-color: #007bff;
        }
        
        .reply-type-option.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        
        .reply-type-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #6c757d;
        }
        
        .reply-type-option.selected .reply-type-icon {
            color: #007bff;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .selection-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: white;
            border-radius: 0.25rem;
            padding: 0.25rem 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
}

@section scripts
{
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <abp-script src="/Pages/Facebook/Campaigns/postSelection.js" />
    <abp-script src="/Pages/Facebook/Campaigns/campaignForm.js" />
    <abp-script src="/Pages/Facebook/Campaigns/create.js" />
}

<div id="facebookCampaignCreateApp">
    <!-- Loading State -->
    <div v-if="loading" class="text-center py-4">
        <div class="loading-spinner"></div>
        <p class="mt-2 text-muted">{{ loadingMessage }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fas fa-plus text-primary me-2"></i>
                            @L["Facebook:CreateCampaign"]
                        </h2>
                        <p class="text-muted mb-0">@L["Facebook:CreateCampaignDescription"]</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary" v-on:click="goBack">
                            <i class="fas fa-arrow-left me-1"></i>
                            @L["Back"]
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Connection Required -->
        <div v-if="!isConnected" class="alert alert-warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>@L["Facebook:ConnectionRequired"]</strong>
                    <br>
                    <span>@L["Facebook:CampaignCreationConnectionRequiredMessage"]</span>
                </div>
                <button type="button" class="btn btn-primary btn-sm" v-on:click="navigateToConnection">
                    <i class="fas fa-link me-1"></i>
                    @L["Facebook:ConnectNow"]
                </button>
            </div>
        </div>

        <!-- Campaign Creation Wizard -->
        <div v-if="isConnected" class="campaign-creation-wizard">
            <!-- Wizard Steps -->
            <div class="wizard-steps">
                <div class="wizard-step" :class="{ active: currentStep === 1, completed: currentStep > 1 }">
                    <div class="step-circle">
                        <i v-if="currentStep > 1" class="fas fa-check"></i>
                        <span v-else>1</span>
                    </div>
                    <div class="step-title">@L["Facebook:SelectPost"]</div>
                </div>
                <div class="wizard-step" :class="{ active: currentStep === 2, completed: currentStep > 2 }">
                    <div class="step-circle">
                        <i v-if="currentStep > 2" class="fas fa-check"></i>
                        <span v-else>2</span>
                    </div>
                    <div class="step-title">@L["Facebook:ConfigureCampaign"]</div>
                </div>
                <div class="wizard-step" :class="{ active: currentStep === 3, completed: currentStep > 3 }">
                    <div class="step-circle">
                        <i v-if="currentStep > 3" class="fas fa-check"></i>
                        <span v-else>3</span>
                    </div>
                    <div class="step-title">@L["Facebook:ReviewAndCreate"]</div>
                </div>
            </div>

            <!-- Wizard Content -->
            <div class="wizard-content">
                <!-- Step 1: Post Selection -->
                <post-selection 
                    v-if="currentStep === 1"
                    ref="postSelectionComponent"
                    :selected-post-id="selectedPostId"
                    :loading="postSelectionLoading"
                    v-on:post-selected="handlePostSelected">
                </post-selection>

                <!-- Step 2: Campaign Configuration -->
                <campaign-form 
                    v-if="currentStep === 2"
                    ref="campaignFormComponent"
                    :campaign-data="campaignData"
                    :selected-post="selectedPost"
                    :loading="formLoading"
                    v-on:form-changed="handleFormChanged"
                    v-on:form-validated="handleFormValidated">
                </campaign-form>

                <!-- Step 3: Review and Create -->
                <div v-if="currentStep === 3" class="review-step">
                    <h4 class="mb-4">@L["Facebook:ReviewCampaign"]</h4>
                    
                    <!-- Campaign Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-section">
                                <h6 class="form-section-title">@L["Facebook:CampaignDetails"]</h6>
                                <dl class="row">
                                    <dt class="col-sm-4">@L["Facebook:CampaignName"]:</dt>
                                    <dd class="col-sm-8">{{ campaignData.campaignName }}</dd>
                                    
                                    <dt class="col-sm-4">@L["Facebook:Description"]:</dt>
                                    <dd class="col-sm-8">{{ campaignData.description || 'N/A' }}</dd>
                                    
                                    <dt class="col-sm-4">@L["Facebook:EndDate"]:</dt>
                                    <dd class="col-sm-8">{{ campaignData.endDate ? formatDate(campaignData.endDate) : 'No end date' }}</dd>
                                </dl>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-section">
                                <h6 class="form-section-title">@L["Facebook:ReplySettings"]</h6>
                                <dl class="row">
                                    <dt class="col-sm-4">@L["Facebook:PublicReply"]:</dt>
                                    <dd class="col-sm-8">
                                        <span v-if="campaignData.sendPublicReply" class="text-success">
                                            <i class="fas fa-check"></i> @L["Enabled"]
                                        </span>
                                        <span v-else class="text-muted">@L["Disabled"]</span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">@L["Facebook:PrivateReply"]:</dt>
                                    <dd class="col-sm-8">
                                        <span v-if="campaignData.sendPrivateReply" class="text-success">
                                            <i class="fas fa-check"></i> @L["Enabled"]
                                        </span>
                                        <span v-else class="text-muted">@L["Disabled"]</span>
                                    </dd>
                                    
                                    <dt class="col-sm-4">@L["Facebook:SendLike"]:</dt>
                                    <dd class="col-sm-8">
                                        <span v-if="campaignData.sendLike" class="text-success">
                                            <i class="fas fa-check"></i> @L["Enabled"]
                                        </span>
                                        <span v-else class="text-muted">@L["Disabled"]</span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="d-flex justify-content-between mt-4 pt-3 border-top">
                    <button type="button" 
                            class="btn btn-outline-secondary" 
                            v-on:click="previousStep" 
                            :disabled="currentStep === 1 || saving">
                        <i class="fas fa-arrow-left me-1"></i>
                        @L["Previous"]
                    </button>
                    
                    <div class="d-flex gap-2">
                        <button type="button" 
                                class="btn btn-outline-secondary" 
                                v-on:click="saveDraft" 
                                :disabled="saving || currentStep === 1">
                            <i class="fas fa-save me-1"></i>
                            @L["Facebook:SaveDraft"]
                        </button>
                        
                        <button v-if="currentStep < 3" 
                                type="button" 
                                class="btn btn-primary" 
                                v-on:click="nextStep" 
                                :disabled="!canProceedToNextStep || saving">
                            @L["Next"]
                            <i class="fas fa-arrow-right ms-1"></i>
                        </button>
                        
                        <button v-if="currentStep === 3" 
                                type="button" 
                                class="btn btn-success" 
                                v-on:click="createCampaign" 
                                :disabled="!canCreateCampaign || saving">
                            <div v-if="saving" class="loading-spinner me-1"></div>
                            <i v-else class="fas fa-check me-1"></i>
                            @L["Facebook:CreateCampaign"]
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
