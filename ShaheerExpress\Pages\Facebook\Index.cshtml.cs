using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ShaheerExpress.Services;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace ShaheerExpress.Pages.Facebook
{
    public class IndexModel : AbpPageModel
    {
        protected IFacebookAuthService _facebookAuthService;

        public IndexModel(IFacebookAuthService facebookAuthService)
        {
            _facebookAuthService = facebookAuthService;
        }

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
