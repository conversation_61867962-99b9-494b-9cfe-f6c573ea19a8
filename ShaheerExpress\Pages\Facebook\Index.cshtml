@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using ShaheerExpress.Permissions
@using ShaheerExpress.Pages.Facebook
@using ShaheerExpress.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using ShaheerExpress.Localization
@inject IHtmlLocalizer<ShaheerExpressResource> L
@inject IAuthorizationService Authorization
@model ShaheerExpress.Pages.Facebook.IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Facebook:Connection"].Value;
    PageLayout.Content.MenuItemName = ShaheerExpressMenus.FacebookConnection;
}

@section styles
{
    <style>
        .facebook-connection-card {
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .facebook-user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .facebook-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .connection-status-badge {
            font-size: 0.875rem;
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
        }
        
        .status-connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .facebook-info-section {
            background-color: #f8f9fc;
            border-radius: 0.35rem;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .feature-list {
            list-style: none;
            padding-left: 0;
        }
        
        .feature-list li {
            padding: 0.25rem 0;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    </style>
}

@section scripts
{
    <abp-script src="/libs/vue/vue.global.js" />
    <abp-script src="/Pages/Facebook/facebookConnectionStatus.js" />
    <abp-script src="/Pages/Facebook/facebookActionButtons.js" />
    <abp-script src="/Pages/Facebook/index.js" />
}

@section content_toolbar {
    <div id="facebookToolbar" v-if="connectionStatus?.isConnected">
        <button type="button" class="btn btn-outline-info btn-sm" v-on:click="refreshStatus" :disabled="loading">
            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            @L["Facebook:RefreshStatus"]
        </button>
    </div>
}

<div id="facebookApp">
    <!-- Loading State -->
    <div v-if="loading" class="text-center py-4">
        <div class="loading-spinner"></div>
        <p class="mt-2 text-muted">{{ loadingMessage }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fab fa-facebook text-primary me-2"></i>
                            @L["Facebook:Connection"]
                        </h2>
                        <p class="text-muted mb-0">@L["Facebook:ConnectionDescription"]</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Redirection Alert -->
        <div v-if="showRedirectionAlert" class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <strong>@L["Facebook:RedirectionNotice"]</strong>
            {{ l('Facebook:RedirectionMessage', redirectSource) }}
            <button type="button" class="btn-close" v-on:click="showRedirectionAlert = false"></button>
        </div>

        <!-- Facebook Connection Status Component -->
        <facebook-connection-status 
            ref="connectionStatusComponent"
            :show-actions="true"
            v-on:connect-clicked="handleConnectClicked"
            v-on:reconnect-clicked="handleReconnectClicked"
            v-on:disconnect-clicked="handleDisconnectClicked"
            v-on:revoke-permissions-clicked="handleRevokePermissionsClicked"
            v-on:status-changed="handleStatusChanged">
        </facebook-connection-status>

        <!-- Advanced Actions Section -->
        <div v-if="connectionStatus?.isConnected" class="facebook-connection-card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-wrench me-2"></i>
                    @L["Facebook:AdvancedActions"]
                </h5>
            </div>
            <div class="card-body">
                <facebook-action-buttons 
                    :show-validate-button="true"
                    :show-disconnect-button="true"
                    :show-revoke-button="true"
                    v-on:action-completed="handleActionCompleted"
                    v-on:error="handleActionError">
                </facebook-action-buttons>
            </div>
        </div>

        <!-- Token Details Section -->
        <div v-if="connectionStatus?.isConnected && showTokenDetails" class="facebook-connection-card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    @L["Facebook:TokenDetails"]
                </h5>
                <button type="button" class="btn btn-sm btn-outline-secondary" v-on:click="showTokenDetails = false">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="card-body">
                <div v-if="tokenValidation">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>@L["Facebook:TokenStatus"]:</strong>
                            <span :class="tokenValidation.isValid ? 'text-success' : 'text-danger'">
                                {{ tokenValidation.isValid ? l('Facebook:Valid') : l('Facebook:Invalid') }}
                            </span>
                        </div>
                        <div class="col-md-6" v-if="tokenValidation.expiresAt">
                            <strong>@L["Facebook:ExpiresAt"]:</strong>
                            {{ formatDateTime(tokenValidation.expiresAt) }}
                        </div>
                    </div>
                    <div v-if="tokenValidation.missingScopes && tokenValidation.missingScopes.length > 0" class="mt-2">
                        <strong>@L["Facebook:MissingScopes"]:</strong>
                        <span class="text-warning">{{ tokenValidation.missingScopes.join(', ') }}</span>
                    </div>
                    <div v-if="tokenValidation.errorMessage" class="mt-2">
                        <strong>@L["Facebook:Error"]:</strong>
                        <span class="text-danger">{{ tokenValidation.errorMessage }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Information Section for Non-Connected Users -->
        <div v-if="!connectionStatus?.isConnected" class="facebook-info-section">
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        @L["Facebook:WhatYouCanDo"]
                    </h5>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6>
                        <i class="fas fa-check-circle text-success me-2"></i>
                        @L["Facebook:PageManagement"]
                    </h6>
                    <ul class="feature-list">
                        <li>@L["Facebook:ImportPages"]</li>
                        <li>@L["Facebook:ViewPosts"]</li>
                        <li>@L["Facebook:MonitorEngagement"]</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>
                        <i class="fas fa-check-circle text-success me-2"></i>
                        @L["Facebook:AutoReplyFeatures"]
                    </h6>
                    <ul class="feature-list">
                        <li>@L["Facebook:AutoReplyComments"]</li>
                        <li>@L["Facebook:CreateCampaigns"]</li>
                        <li>@L["Facebook:ManageKeywords"]</li>
                        <li>@L["Facebook:ViewAnalytics"]</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
