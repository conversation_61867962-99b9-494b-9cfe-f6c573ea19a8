const CampaignActions = {
    props: {
        campaign: {
            type: Object,
            required: true
        },
        canEdit: {
            type: Boolean,
            default: false
        },
        canDelete: {
            type: Boolean,
            default: false
        },
        canActivate: {
            type: Boolean,
            default: false
        },
        canDeactivate: {
            type: Boolean,
            default: false
        }
    },
    
    emits: [
        'activate-campaign',
        'deactivate-campaign',
        'edit-campaign',
        'delete-campaign',
        'view-details',
        'view-activities'
    ],
    
    data() {
        return {
            l: null
        };
    },
    
    computed: {
        isActive() {
            return this.campaign.isActive === true;
        },
        
        isEnded() {
            return this.campaign.endDate && new Date(this.campaign.endDate) < new Date();
        },
        
        canActivateThisCampaign() {
            return this.canActivate && !this.isActive && !this.isEnded;
        },
        
        canDeactivateThisCampaign() {
            return this.canDeactivate && this.isActive;
        }
    },
    
    template: `
        <div class="campaign-actions">
            <div class="d-flex gap-1 flex-wrap">
                <!-- Quick Actions -->
                <button v-if="canActivateThisCampaign" 
                        type="button" 
                        class="btn btn-success btn-sm" 
                        v-on:click="activateCampaign"
                        :title="l('Facebook:ActivateCampaign') || 'Activate Campaign'">
                    <i class="fas fa-play"></i>
                </button>
                
                <button v-if="canDeactivateThisCampaign" 
                        type="button" 
                        class="btn btn-warning btn-sm" 
                        v-on:click="deactivateCampaign"
                        :title="l('Facebook:DeactivateCampaign') || 'Deactivate Campaign'">
                    <i class="fas fa-pause"></i>
                </button>
                
                <!-- View Details Button -->
                <button type="button" 
                        class="btn btn-info btn-sm" 
                        v-on:click="viewDetails"
                        :title="l('Facebook:ViewDetails') || 'View Details'">
                    <i class="fas fa-eye"></i>
                </button>
                
                <!-- More Actions Dropdown -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                            type="button" 
                            :id="'campaignActions' + campaign.id"
                            data-bs-toggle="dropdown" 
                            aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu campaign-actions-dropdown" :aria-labelledby="'campaignActions' + campaign.id">
                        <!-- View Actions -->
                        <li>
                            <a class="dropdown-item" href="#" v-on:click.prevent="viewDetails">
                                <i class="fas fa-eye me-2"></i>
                                {{ l('Facebook:ViewDetails') || 'View Details' }}
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" v-on:click.prevent="viewActivities">
                                <i class="fas fa-history me-2"></i>
                                {{ l('Facebook:ViewActivities') || 'View Activities' }}
                            </a>
                        </li>
                        
                        <li><hr class="dropdown-divider"></li>
                        
                        <!-- Status Actions -->
                        <li v-if="canActivateThisCampaign">
                            <a class="dropdown-item text-success" href="#" v-on:click.prevent="activateCampaign">
                                <i class="fas fa-play me-2"></i>
                                {{ l('Facebook:ActivateCampaign') || 'Activate Campaign' }}
                            </a>
                        </li>
                        <li v-if="canDeactivateThisCampaign">
                            <a class="dropdown-item text-warning" href="#" v-on:click.prevent="deactivateCampaign">
                                <i class="fas fa-pause me-2"></i>
                                {{ l('Facebook:DeactivateCampaign') || 'Deactivate Campaign' }}
                            </a>
                        </li>
                        
                        <!-- Edit Actions -->
                        <li v-if="canEdit">
                            <hr class="dropdown-divider">
                            <a class="dropdown-item" href="#" v-on:click.prevent="editCampaign">
                                <i class="fas fa-edit me-2"></i>
                                {{ l('Facebook:EditCampaign') || 'Edit Campaign' }}
                            </a>
                        </li>
                        
                        <!-- Delete Actions -->
                        <li v-if="canDelete">
                            <hr class="dropdown-divider">
                            <a class="dropdown-item text-danger" href="#" v-on:click.prevent="deleteCampaign">
                                <i class="fas fa-trash me-2"></i>
                                {{ l('Facebook:DeleteCampaign') || 'Delete Campaign' }}
                            </a>
                        </li>
                        
                        <!-- Additional Actions -->
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="#" v-on:click.prevent="duplicateCampaign">
                                <i class="fas fa-copy me-2"></i>
                                {{ l('Facebook:DuplicateCampaign') || 'Duplicate Campaign' }}
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" v-on:click.prevent="exportCampaignData">
                                <i class="fas fa-download me-2"></i>
                                {{ l('Facebook:ExportData') || 'Export Data' }}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        activateCampaign() {
            this.$emit('activate-campaign', this.campaign.id);
        },
        
        deactivateCampaign() {
            this.$emit('deactivate-campaign', this.campaign.id);
        },
        
        editCampaign() {
            this.$emit('edit-campaign', this.campaign.id);
        },
        
        deleteCampaign() {
            this.$emit('delete-campaign', this.campaign.id);
        },
        
        viewDetails() {
            this.$emit('view-details', this.campaign.id);
        },
        
        viewActivities() {
            this.$emit('view-activities', this.campaign.id);
        },
        
        duplicateCampaign() {
            // Navigate to create campaign page with this campaign as template
            window.location.href = `/Facebook/Campaigns/Create?templateId=${this.campaign.id}`;
        },
        
        async exportCampaignData() {
            try {
                // This would typically call a backend service to export campaign data
                abp.notify.info(this.l('Facebook:ExportingData') || 'Exporting campaign data...');
                
                // For now, just show a placeholder message
                setTimeout(() => {
                    abp.notify.success(this.l('Facebook:ExportCompleted') || 'Export completed successfully');
                }, 2000);
                
            } catch (error) {
                console.error('Error exporting campaign data:', error);
                abp.notify.error(this.l('Facebook:ErrorExportingData') || 'Error exporting campaign data');
            }
        }
    },
    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },    
    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    }
};
