using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ShaheerExpress.Services.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace ShaheerExpress.Services;

public interface IFacebookPostPublishingService : IApplicationService
{
    /// <summary>
    /// Publishes a post immediately or schedules it for later
    /// </summary>
    Task<FacebookPostPublishResultDto> PublishPostAsync(PublishFacebookPostDto input);

    /// <summary>
    /// Generates a preview of how the post will appear on Facebook
    /// </summary>
    Task<FacebookPostPreviewDto> GeneratePostPreviewAsync(PublishFacebookPostDto input);

    /// <summary>
    /// Gets link preview information for a given URL
    /// </summary>
    Task<FacebookLinkPreviewDto> GetLinkPreviewAsync(string url);

    /// <summary>
    /// Gets list of scheduled posts
    /// </summary>
    Task<PagedResultDto<ScheduledFacebookPostDto>> GetScheduledPostsAsync(GetScheduledPostsInput input);

    /// <summary>
    /// Gets a specific scheduled post by ID
    /// </summary>
    Task<ScheduledFacebookPostDto> GetScheduledPostAsync(Guid id);

    /// <summary>
    /// Updates a scheduled post (only if not yet published)
    /// </summary>
    Task<ScheduledFacebookPostDto> UpdateScheduledPostAsync(Guid id, UpdateScheduledPostDto input);

    /// <summary>
    /// Cancels a scheduled post
    /// </summary>
    Task CancelScheduledPostAsync(Guid id);

    /// <summary>
    /// Publishes a scheduled post immediately
    /// </summary>
    Task<FacebookPostPublishResultDto> PublishScheduledPostNowAsync(Guid id);

    /// <summary>
    /// Validates that the user has permission to post to the specified page
    /// </summary>
    Task<bool> CanPublishToPageAsync(Guid facebookPageId);

    /// <summary>
    /// Gets available Facebook pages for posting
    /// </summary>
    Task<List<FacebookPageDto>> GetAvailablePagesForPostingAsync();
}
