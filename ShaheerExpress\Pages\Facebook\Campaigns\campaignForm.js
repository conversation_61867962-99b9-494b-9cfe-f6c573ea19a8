const CampaignForm = {
    props: {
        campaignData: {
            type: Object,
            default: () => ({})
        },
        selectedPost: {
            type: Object,
            default: null
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    
    emits: ['form-changed', 'form-validated'],
    
    data() {
        return {
            form: {
                campaignName: '',
                description: '',
                publicReplyMessage: '',
                privateReplyMessage: '',
                sendPublicReply: true,
                sendPrivateReply: false,
                sendLike: false,
                endDate: null,
                maxRepliesPerUser: 0,
                includePostLinkInPrivateReply: false,
                publicReplyType: 'Custom',
                privateReplyType: 'TextReply'
            },
            validation: {
                campaignName: { isValid: false, message: '' },
                publicReplyMessage: { isValid: false, message: '' },
                privateReplyMessage: { isValid: true, message: '' }
            },
            l: null
        };
    },
    
    computed: {
        isFormValid() {
            return this.validation.campaignName.isValid &&
                   (!this.form.sendPublicReply || this.validation.publicReplyMessage.isValid) &&
                   (!this.form.sendPrivateReply || this.validation.privateReplyMessage.isValid);
        },
        
        hasSelectedPost() {
            return this.selectedPost && this.selectedPost.facebookPostId;
        }
    },
    
    template: `
        <div class="campaign-form">
            <h4 class="mb-4">{{ l('Facebook:ConfigureCampaign') || 'Configure Campaign' }}</h4>
            
            <!-- Selected Post Preview -->
            <div v-if="hasSelectedPost" class="form-section mb-4">
                <h6 class="form-section-title">{{ l('Facebook:SelectedPost') || 'Selected Post' }}</h6>
                <div class="post-preview">
                    <img v-if="selectedPost.pictureUrl" 
                         :src="selectedPost.pictureUrl" 
                         :alt="'Post thumbnail'"
                         class="post-thumbnail">
                    <div v-else class="post-thumbnail-placeholder">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="post-content">
                        <div class="post-message">{{ selectedPost.message || '[No text content]' }}</div>
                        <div class="post-meta">
                            <strong>{{ selectedPost.pageName }}</strong> • 
                            {{ formatDate(selectedPost.facebookCreatedTime) }}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Basic Campaign Information -->
            <div class="form-section">
                <h6 class="form-section-title">{{ l('Facebook:BasicInformation') || 'Basic Information' }}</h6>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">{{ l('Facebook:CampaignName') || 'Campaign Name' }} *</label>
                        <input type="text" 
                               class="form-control" 
                               :class="{ 'is-invalid': !validation.campaignName.isValid && validation.campaignName.message }"
                               v-model="form.campaignName"
                               v-on:input="validateCampaignName"
                               v-on:blur="validateCampaignName"
                               :placeholder="l('Facebook:EnterCampaignName') || 'Enter campaign name'"
                               :disabled="loading">
                        <div v-if="!validation.campaignName.isValid && validation.campaignName.message" class="invalid-feedback">
                            {{ validation.campaignName.message }}
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label">{{ l('Facebook:EndDate') || 'End Date' }}</label>
                        <input type="datetime-local" 
                               class="form-control" 
                               v-model="form.endDate"
                               v-on:change="emitFormChanged"
                               :disabled="loading">
                        <small class="form-text text-muted">{{ l('Facebook:EndDateHelp') || 'Leave empty for no end date' }}</small>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">{{ l('Facebook:Description') || 'Description' }}</label>
                    <textarea class="form-control" 
                              rows="3"
                              v-model="form.description"
                              v-on:input="emitFormChanged"
                              :placeholder="l('Facebook:EnterDescription') || 'Enter campaign description (optional)'"
                              :disabled="loading"></textarea>
                </div>
            </div>
            
            <!-- Reply Configuration -->
            <div class="form-section">
                <h6 class="form-section-title">{{ l('Facebook:ReplyConfiguration') || 'Reply Configuration' }}</h6>
                
                <!-- Reply Type Selection -->
                <div class="reply-type-selector mb-4">
                    <div class="reply-type-option" 
                         :class="{ selected: form.sendPublicReply }"
                         v-on:click="togglePublicReply">
                        <div class="reply-type-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <h6>{{ l('Facebook:PublicReply') || 'Public Reply' }}</h6>
                        <p class="text-muted mb-0">{{ l('Facebook:PublicReplyDescription') || 'Reply publicly to comments' }}</p>
                    </div>
                    
                    <div class="reply-type-option" 
                         :class="{ selected: form.sendPrivateReply }"
                         v-on:click="togglePrivateReply">
                        <div class="reply-type-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h6>{{ l('Facebook:PrivateReply') || 'Private Reply' }}</h6>
                        <p class="text-muted mb-0">{{ l('Facebook:PrivateReplyDescription') || 'Send private messages' }}</p>
                    </div>
                    
                    <div class="reply-type-option" 
                         :class="{ selected: form.sendLike }"
                         v-on:click="toggleLike">
                        <div class="reply-type-icon">
                            <i class="fas fa-thumbs-up"></i>
                        </div>
                        <h6>{{ l('Facebook:SendLike') || 'Send Like' }}</h6>
                        <p class="text-muted mb-0">{{ l('Facebook:SendLikeDescription') || 'Like comments automatically' }}</p>
                    </div>
                </div>
                
                <!-- Public Reply Message -->
                <div v-if="form.sendPublicReply" class="mb-3">
                    <label class="form-label">{{ l('Facebook:PublicReplyMessage') || 'Public Reply Message' }} *</label>
                    <textarea class="form-control" 
                              rows="3"
                              :class="{ 'is-invalid': !validation.publicReplyMessage.isValid && validation.publicReplyMessage.message }"
                              v-model="form.publicReplyMessage"
                              v-on:input="validatePublicReplyMessage"
                              v-on:blur="validatePublicReplyMessage"
                              :placeholder="l('Facebook:EnterPublicReplyMessage') || 'Enter the message to reply publicly to comments'"
                              :disabled="loading"></textarea>
                    <div v-if="!validation.publicReplyMessage.isValid && validation.publicReplyMessage.message" class="invalid-feedback">
                        {{ validation.publicReplyMessage.message }}
                    </div>
                </div>
                
                <!-- Private Reply Message -->
                <div v-if="form.sendPrivateReply" class="mb-3">
                    <label class="form-label">{{ l('Facebook:PrivateReplyMessage') || 'Private Reply Message' }} *</label>
                    <textarea class="form-control" 
                              rows="3"
                              :class="{ 'is-invalid': !validation.privateReplyMessage.isValid && validation.privateReplyMessage.message }"
                              v-model="form.privateReplyMessage"
                              v-on:input="validatePrivateReplyMessage"
                              v-on:blur="validatePrivateReplyMessage"
                              :placeholder="l('Facebook:EnterPrivateReplyMessage') || 'Enter the message to send privately'"
                              :disabled="loading"></textarea>
                    <div v-if="!validation.privateReplyMessage.isValid && validation.privateReplyMessage.message" class="invalid-feedback">
                        {{ validation.privateReplyMessage.message }}
                    </div>
                    
                    <div class="form-check mt-2">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="includePostLink"
                               v-model="form.includePostLinkInPrivateReply"
                               v-on:change="emitFormChanged"
                               :disabled="loading">
                        <label class="form-check-label" for="includePostLink">
                            {{ l('Facebook:IncludePostLinkInPrivateReply') || 'Include post link in private reply' }}
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Settings -->
            <div class="form-section">
                <h6 class="form-section-title">{{ l('Facebook:AdvancedSettings') || 'Advanced Settings' }}</h6>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">{{ l('Facebook:MaxRepliesPerUser') || 'Max Replies Per User' }}</label>
                        <input type="number" 
                               class="form-control" 
                               v-model.number="form.maxRepliesPerUser"
                               v-on:input="emitFormChanged"
                               min="0"
                               :placeholder="l('Facebook:EnterMaxReplies') || '0 for unlimited'"
                               :disabled="loading">
                        <small class="form-text text-muted">{{ l('Facebook:MaxRepliesHelp') || 'Maximum number of replies to send to each user (0 = unlimited)' }}</small>
                    </div>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        validateCampaignName() {
            if (!this.form.campaignName || this.form.campaignName.trim().length === 0) {
                this.validation.campaignName = {
                    isValid: false,
                    message: this.l('Facebook:CampaignNameRequired') || 'Campaign name is required'
                };
            } else if (this.form.campaignName.trim().length < 3) {
                this.validation.campaignName = {
                    isValid: false,
                    message: this.l('Facebook:CampaignNameTooShort') || 'Campaign name must be at least 3 characters'
                };
            } else {
                this.validation.campaignName = {
                    isValid: true,
                    message: ''
                };
            }
            this.emitFormValidated();
        },
        
        validatePublicReplyMessage() {
            if (this.form.sendPublicReply) {
                if (!this.form.publicReplyMessage || this.form.publicReplyMessage.trim().length === 0) {
                    this.validation.publicReplyMessage = {
                        isValid: false,
                        message: this.l('Facebook:PublicReplyMessageRequired') || 'Public reply message is required'
                    };
                } else {
                    this.validation.publicReplyMessage = {
                        isValid: true,
                        message: ''
                    };
                }
            } else {
                this.validation.publicReplyMessage = {
                    isValid: true,
                    message: ''
                };
            }
            this.emitFormValidated();
        },
        
        validatePrivateReplyMessage() {
            if (this.form.sendPrivateReply) {
                if (!this.form.privateReplyMessage || this.form.privateReplyMessage.trim().length === 0) {
                    this.validation.privateReplyMessage = {
                        isValid: false,
                        message: this.l('Facebook:PrivateReplyMessageRequired') || 'Private reply message is required'
                    };
                } else {
                    this.validation.privateReplyMessage = {
                        isValid: true,
                        message: ''
                    };
                }
            } else {
                this.validation.privateReplyMessage = {
                    isValid: true,
                    message: ''
                };
            }
            this.emitFormValidated();
        },
        
        togglePublicReply() {
            this.form.sendPublicReply = !this.form.sendPublicReply;
            this.validatePublicReplyMessage();
            this.emitFormChanged();
        },
        
        togglePrivateReply() {
            this.form.sendPrivateReply = !this.form.sendPrivateReply;
            this.validatePrivateReplyMessage();
            this.emitFormChanged();
        },
        
        toggleLike() {
            this.form.sendLike = !this.form.sendLike;
            this.emitFormChanged();
        },
        
        emitFormChanged() {
            this.$emit('form-changed', { ...this.form });
        },
        
        emitFormValidated() {
            this.$emit('form-validated', this.isFormValid);
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric' 
            });
        },
        
        // Public method to get form data
        getFormData() {
            return { ...this.form };
        },
        
        // Public method to validate entire form
        validateForm() {
            this.validateCampaignName();
            this.validatePublicReplyMessage();
            this.validatePrivateReplyMessage();
            return this.isFormValid;
        }
    },
    
    watch: {
        campaignData: {
            handler(newData) {
                if (newData) {
                    this.form = { ...this.form, ...newData };
                }
            },
            immediate: true,
            deep: true
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },

    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
        
        // Initialize form with campaign data
        if (this.campaignData) {
            this.form = { ...this.form, ...this.campaignData };
        }
        
        // Initial validation
        this.validateCampaignName();
        this.validatePublicReplyMessage();
        this.validatePrivateReplyMessage();
    }
};
