using System.ComponentModel.DataAnnotations;

namespace ShaheerExpress.Services.Dtos;

public enum CardReplyButtonType
{
    WebUrl = 0,
    PhoneNumber = 1
}

public class CardReplyButton
{
    [Required]
    [StringLength(20)]
    public string Title { get; set; } = string.Empty;

    public CardReplyButtonType Type { get; set; } = CardReplyButtonType.WebUrl;

    [StringLength(2048)]
    public string? Url { get; set; }

    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    public bool IsValid()
    {
        if (string.IsNullOrWhiteSpace(Title))
            return false;

        return Type switch
        {
            CardReplyButtonType.WebUrl => !string.IsNullOrWhiteSpace(Url),
            CardReplyButtonType.PhoneNumber => !string.IsNullOrWhiteSpace(PhoneNumber),
            _ => false
        };
    }
}

public class CardReplyData
{
    [Required]
    [StringLength(80)]
    public string Title { get; set; } = string.Empty;

    [StringLength(2048)]
    public string? ImageUrl { get; set; }

    [StringLength(80)]
    public string? Subtitle { get; set; }

    public List<CardReplyButton> Buttons { get; set; } = new();

    public bool IsValid()
    {
        // Title is always required
        if (string.IsNullOrWhiteSpace(Title))
            return false;

        // At least one additional field must be provided
        bool hasAdditionalField = !string.IsNullOrWhiteSpace(ImageUrl) ||
                                 !string.IsNullOrWhiteSpace(Subtitle) ||
                                 Buttons.Any();

        if (!hasAdditionalField)
            return false;

        // Maximum of 3 buttons allowed
        if (Buttons.Count > 3)
            return false;

        // All buttons must be valid
        return Buttons.All(b => b.IsValid());
    }

    public bool HasImage => !string.IsNullOrWhiteSpace(ImageUrl);
    public bool HasSubtitle => !string.IsNullOrWhiteSpace(Subtitle);
    public bool HasButtons => Buttons.Any();
}
