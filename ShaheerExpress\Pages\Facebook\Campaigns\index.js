function initializeVueApp() {
    if (typeof Vue === 'undefined') {
        setTimeout(initializeVueApp, 100);
        return;
    }
    else
        createVueApp();
}

function createVueApp() {
    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                campaigns: [],
                availablePages: [],
                totalCount: 0,
                currentPage: 1,
                pageSize: 10,
                currentSorting: 'creationTime desc',
                loading: false,
                campaignsLoading: false,
                filtersLoading: false,
                loadingMessage: 'Loading...',
                isConnected: false,
                performanceSummary: null,
                l: null,
                
                // Filters
                filters: {
                    facebookPageId: null,
                    isActive: null,
                    searchText: '',
                    fromDate: null,
                    toDate: null
                },
                
                // Permissions
                canCreateCampaign: false,
                canEditCampaign: false,
                canDeleteCampaign: false,
                canActivateCampaign: false,
                canDeactivateCampaign: false,
                canViewCampaigns: false
            };
        },
        
        methods: {
            async loadInitialData() {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Loading Facebook connection status...';
                    
                    // Check if user is connected to Facebook
                    this.isConnected = await window.shaheerExpress.services.facebookAuth.isConnectedToFacebook();
                    
                    if (this.isConnected) {
                        await Promise.all([
                            this.loadAvailablePages(),
                            this.loadCampaigns(),
                            this.loadPerformanceSummary()
                        ]);
                    }
                } catch (error) {
                    console.error('Error loading initial data:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingData') || 'Error loading data');
                } finally {
                    this.loading = false;
                }
            },
            
            async loadAvailablePages() {
                try {
                    this.filtersLoading = true;
                    const response = await window.shaheerExpress.services.facebookPage.getList({
                        maxResultCount: 1000,
                        skipCount: 0
                    });
                    this.availablePages = response.items;
                } catch (error) {
                    console.error('Error loading available pages:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingPages') || 'Error loading Facebook pages');
                } finally {
                    this.filtersLoading = false;
                }
            },
            
            async loadCampaigns() {
                try {
                    this.campaignsLoading = true;
                    
                    const params = {
                        skipCount: (this.currentPage - 1) * this.pageSize,
                        maxResultCount: this.pageSize,
                        sorting: this.currentSorting,
                        facebookPageId: this.filters.facebookPageId,
                        isActive: this.filters.isActive,
                        searchText: this.filters.searchText,
                        fromDate: this.filters.fromDate,
                        toDate: this.filters.toDate
                    };
                    
                    const response = await window.shaheerExpress.services.autoReplyCampaign.getList(params);
                    this.campaigns = response.items;
                    this.totalCount = response.totalCount;
                } catch (error) {
                    console.error('Error loading campaigns:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingCampaigns') || 'Error loading campaigns');
                } finally {
                    this.campaignsLoading = false;
                }
            },
            
            async loadPerformanceSummary() {
                try {
                    // Calculate performance summary from campaigns
                    if (this.campaigns && this.campaigns.length > 0) {
                        this.performanceSummary = {
                            totalCampaigns: this.totalCount,
                            activeCampaigns: this.campaigns.filter(c => c.isActive).length,
                            totalReplies: this.campaigns.reduce((sum, c) => sum + (c.totalRepliesSent || 0), 0),
                            totalLikes: this.campaigns.reduce((sum, c) => sum + (c.likesSent || 0), 0)
                        };
                    } else {
                        this.performanceSummary = {
                            totalCampaigns: 0,
                            activeCampaigns: 0,
                            totalReplies: 0,
                            totalLikes: 0
                        };
                    }
                } catch (error) {
                    console.error('Error calculating performance summary:', error);
                }
            },
            
            async refreshCampaigns() {
                await this.loadCampaigns();
                await this.loadPerformanceSummary();
            },
            
            navigateToConnection() {
                window.location.href = '/Facebook/Connection';
            },
            
            navigateToCreateCampaign() {
                window.location.href = '/Facebook/Campaigns/Create';
            },
            
            handleFiltersChanged(newFilters) {
                this.filters = { ...newFilters };
                this.currentPage = 1; // Reset to first page when filters change
                this.loadCampaigns();
            },
            
            handlePageChanged(page) {
                this.currentPage = page;
                this.loadCampaigns();
            },
            
            handleSortChanged(sorting) {
                this.currentSorting = sorting;
                this.currentPage = 1; // Reset to first page when sorting changes
                this.loadCampaigns();
            },
            
            async handleActivateCampaign(campaignId) {
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:ActivateCampaignConfirmation') || 'Are you sure you want to activate this campaign?',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Activating campaign...';
                        
                        await window.shaheerExpress.services.autoReplyCampaign.activate(campaignId);
                        abp.notify.success(this.l('Facebook:CampaignActivatedSuccess') || 'Campaign activated successfully');
                        
                        // Reload campaigns
                        await this.refreshCampaigns();
                    } catch (error) {
                        console.error('Error activating campaign:', error);
                        abp.notify.error(this.l('Facebook:ErrorActivatingCampaign') || 'Error activating campaign');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            async handleDeactivateCampaign(campaignId) {
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:DeactivateCampaignConfirmation') || 'Are you sure you want to deactivate this campaign?',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Deactivating campaign...';
                        
                        await window.shaheerExpress.services.autoReplyCampaign.deactivate(campaignId);
                        abp.notify.success(this.l('Facebook:CampaignDeactivatedSuccess') || 'Campaign deactivated successfully');
                        
                        // Reload campaigns
                        await this.refreshCampaigns();
                    } catch (error) {
                        console.error('Error deactivating campaign:', error);
                        abp.notify.error(this.l('Facebook:ErrorDeactivatingCampaign') || 'Error deactivating campaign');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            handleEditCampaign(campaignId) {
                // Navigate to campaign edit page
                window.location.href = `/Facebook/Campaigns/Edit/${campaignId}`;
            },
            
            async handleDeleteCampaign(campaignId) {
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:DeleteCampaignConfirmation') || 'Are you sure you want to delete this campaign? This action cannot be undone.',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Deleting campaign...';
                        
                        await window.shaheerExpress.services.autoReplyCampaign.delete(campaignId);
                        abp.notify.success(this.l('Facebook:CampaignDeletedSuccess') || 'Campaign deleted successfully');
                        
                        // Reload campaigns
                        await this.refreshCampaigns();
                    } catch (error) {
                        console.error('Error deleting campaign:', error);
                        abp.notify.error(this.l('Facebook:ErrorDeletingCampaign') || 'Error deleting campaign');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            handleViewDetails(campaignId) {
                // Navigate to campaign details page
                window.location.href = `/Facebook/Campaigns/Details/${campaignId}`;
            },
            
            handleViewActivities(campaignId) {
                // Navigate to campaign activities page
                window.location.href = `/Facebook/Campaigns/Activities/${campaignId}`;
            },
            
            formatNumber(num) {
                if (!num) return '0';
                return num.toLocaleString();
            },
            
            // URL parameter handling
            checkUrlParameters() {
                const urlParams = new URLSearchParams(window.location.search);
                
                // Check for page ID parameter
                if (urlParams.has('pageId')) {
                    const pageId = urlParams.get('pageId');
                    if (pageId) {
                        this.filters.facebookPageId = pageId;
                    }
                }
                
                // Check for status filter parameter
                if (urlParams.has('isActive')) {
                    const isActive = urlParams.get('isActive');
                    if (isActive === 'true') {
                        this.filters.isActive = true;
                    } else if (isActive === 'false') {
                        this.filters.isActive = false;
                    }
                }
            }
        },
        async created() {
            // Load localization earlier in lifecycle
            try {
                if (typeof abp !== 'undefined' && abp.localization) {
                    this.l = abp.localization.getResource('ShaheerExpress');
                }
            } catch (e) {
                console.error('Localization init error:', e);
                // Keep fallback function
            }
        },        
        async mounted() {
            // Initialize ABP localization
            this.l = abp.localization.getResource('ShaheerExpress');
            
            // Check permissions
            this.canViewCampaigns = abp.auth.isGranted('ShaheerExpress.Campaigns.View');
            this.canCreateCampaign = abp.auth.isGranted('ShaheerExpress.Campaigns.Create');
            this.canEditCampaign = abp.auth.isGranted('ShaheerExpress.Campaigns.Edit');
            this.canDeleteCampaign = abp.auth.isGranted('ShaheerExpress.Campaigns.Delete');
            this.canActivateCampaign = abp.auth.isGranted('ShaheerExpress.Campaigns.Activate');
            this.canDeactivateCampaign = abp.auth.isGranted('ShaheerExpress.Campaigns.Deactivate');
            
            // Check URL parameters
            this.checkUrlParameters();
            
            // Load initial data
            await this.loadInitialData();
        }
    });
    
    // Register components (will be defined in separate files)
    app.component('campaign-list', CampaignList);
    app.component('campaign-filters', CampaignFilters);
    app.component('campaign-actions', CampaignActions);
    
    app.mount('#facebookCampaignsApp');
}

// Initialize the Vue app when DOM is ready
document.addEventListener('DOMContentLoaded', initializeVueApp);
