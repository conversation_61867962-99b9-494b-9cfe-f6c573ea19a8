using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace ShaheerExpress.Services.Dtos;

public class FacebookUserDto : FullAuditedEntityDto<Guid>
{
    public string FacebookId { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string FacebookEmail { get; set; } = string.Empty;
    public string FacebookName { get; set; } = string.Empty;
    public string ProfilePictureUrl { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
}

public class CreateFacebookUserDto
{
    public string FacebookId { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string AccessToken { get; set; } = string.Empty;
    public string FacebookEmail { get; set; } = string.Empty;
    public string FacebookName { get; set; } = string.Empty;
    public string? RefreshToken { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
    public string? ProfilePictureUrl { get; set; }
}

public class UpdateFacebookUserDto
{
    public string? FacebookName { get; set; }
    public string? ProfilePictureUrl { get; set; }
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
}

public class FacebookConnectionStatusDto
{
    public bool IsConnected { get; set; }
    public bool IsTokenValid { get; set; }
    public bool IsTokenExpired { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
    public List<string> MissingScopes { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public bool RequiresReconnection { get; set; }
    public FacebookUserDto? FacebookUser { get; set; }
}

public class FacebookTokenValidationDto
{
    public bool IsValid { get; set; }
    public bool IsExpired { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public List<string> Scopes { get; set; } = new();
    public List<string> MissingScopes { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public int? ErrorCode { get; set; }
    public string TokenType { get; set; } = string.Empty;
}

public class FacebookPageTokenStatusDto
{
    public Guid PageId { get; set; }
    public string PageName { get; set; } = string.Empty;
    public bool IsTokenValid { get; set; }
    public bool IsTokenExpired { get; set; }
    public DateTime? TokenExpiresAt { get; set; }
    public List<string> MissingScopes { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public bool RequiresReconnection { get; set; }
}
