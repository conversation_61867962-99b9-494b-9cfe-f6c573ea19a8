using Microsoft.Extensions.Localization;
using ShaheerExpress.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace ShaheerExpress;

[Dependency(ReplaceServices = true)]
public class ShaheerExpressBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<ShaheerExpressResource> _localizer;

    public ShaheerExpressBrandingProvider(IStringLocalizer<ShaheerExpressResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}