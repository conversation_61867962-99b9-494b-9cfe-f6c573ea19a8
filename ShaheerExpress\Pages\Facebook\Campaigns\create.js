function initializeVueApp() {
    if (typeof Vue === 'undefined') {
        setTimeout(initializeVueApp, 100);
        return;
    }
    else
        createVueApp();
}

function createVueApp() {
    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                currentStep: 1,
                loading: false,
                postSelectionLoading: false,
                formLoading: false,
                saving: false,
                loadingMessage: 'Loading...',
                isConnected: false,
                l: null,
                
                // Selected data
                selectedPost: null,
                selectedPostId: null,
                campaignData: {
                    campaignName: '',
                    description: '',
                    publicReplyMessage: '',
                    privateReplyMessage: '',
                    sendPublicReply: true,
                    sendPrivateReply: false,
                    sendLike: false,
                    endDate: null,
                    maxRepliesPerUser: 0,
                    includePostLinkInPrivateReply: false,
                    publicReplyType: 'Custom',
                    privateReplyType: 'TextReply'
                },
                
                // Validation states
                canProceedToNextStep: false,
                canCreateCampaign: false,
                isFormValid: false,
                
                // Permissions
                canCreate: false
            };
        },
        
        methods: {
            async loadInitialData() {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Loading Facebook connection status...';
                    
                    // Check if user is connected to Facebook
                    this.isConnected = await window.shaheerExpress.services.facebookAuth.isConnectedToFacebook();
                    
                    if (this.isConnected) {
                        // Check URL parameters for pre-selected post
                        this.checkUrlParameters();
                        
                        // Load template data if specified
                        await this.loadTemplateData();
                    }
                } catch (error) {
                    console.error('Error loading initial data:', error);
                    abp.notify.error(this.l('Facebook:ErrorLoadingData') || 'Error loading data');
                } finally {
                    this.loading = false;
                }
            },
            
            checkUrlParameters() {
                const urlParams = new URLSearchParams(window.location.search);
                
                // Check for pre-selected post ID
                if (urlParams.has('postId')) {
                    this.selectedPostId = urlParams.get('postId');
                }
                
                // Check for template campaign ID
                if (urlParams.has('templateId')) {
                    this.templateId = urlParams.get('templateId');
                }
            },
            
            async loadTemplateData() {
                if (this.templateId) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Loading template data...';
                        
                        const template = await window.shaheerExpress.services.autoReplyCampaign.get(this.templateId);
                        
                        // Copy template data to campaign data (excluding ID and timestamps)
                        this.campaignData = {
                            campaignName: template.campaignName + ' (Copy)',
                            description: template.description,
                            publicReplyMessage: template.publicReplyMessage,
                            privateReplyMessage: template.privateReplyMessage,
                            sendPublicReply: template.sendPublicReply,
                            sendPrivateReply: template.sendPrivateReply,
                            sendLike: template.sendLike,
                            endDate: null, // Don't copy end date
                            maxRepliesPerUser: template.maxRepliesPerUser,
                            includePostLinkInPrivateReply: template.includePostLinkInPrivateReply,
                            publicReplyType: template.publicReplyType,
                            privateReplyType: template.privateReplyType
                        };
                        
                        abp.notify.info(this.l('Facebook:TemplateLoaded') || 'Template data loaded successfully');
                    } catch (error) {
                        console.error('Error loading template data:', error);
                        abp.notify.error(this.l('Facebook:ErrorLoadingTemplate') || 'Error loading template data');
                    }
                }
            },
            
            navigateToConnection() {
                window.location.href = '/Facebook/Connection';
            },
            
            goBack() {
                window.location.href = '/Facebook/Campaigns';
            },
            
            nextStep() {
                if (this.canProceedToNextStep) {
                    this.currentStep++;
                    this.updateStepValidation();
                }
            },
            
            previousStep() {
                if (this.currentStep > 1) {
                    this.currentStep--;
                    this.updateStepValidation();
                }
            },
            
            updateStepValidation() {
                switch (this.currentStep) {
                    case 1:
                        this.canProceedToNextStep = this.selectedPost !== null;
                        break;
                    case 2:
                        this.canProceedToNextStep = this.isFormValid;
                        break;
                    case 3:
                        this.canCreateCampaign = this.selectedPost !== null && this.isFormValid;
                        break;
                    default:
                        this.canProceedToNextStep = false;
                        this.canCreateCampaign = false;
                }
            },
            
            handlePostSelected(post) {
                this.selectedPost = post;
                this.updateStepValidation();
            },
            
            handleFormChanged(formData) {
                this.campaignData = { ...this.campaignData, ...formData };
            },
            
            handleFormValidated(isValid) {
                this.isFormValid = isValid;
                this.updateStepValidation();
            },
            
            async saveDraft() {
                if (!this.selectedPost || this.currentStep === 1) {
                    abp.notify.warn(this.l('Facebook:SelectPostFirst') || 'Please select a post first');
                    return;
                }
                
                try {
                    this.saving = true;
                    
                    // For now, just save to localStorage as a draft
                    const draftData = {
                        selectedPost: this.selectedPost,
                        campaignData: this.campaignData,
                        timestamp: new Date().toISOString()
                    };
                    
                    localStorage.setItem('campaignDraft', JSON.stringify(draftData));
                    abp.notify.success(this.l('Facebook:DraftSaved') || 'Draft saved successfully');
                    
                } catch (error) {
                    console.error('Error saving draft:', error);
                    abp.notify.error(this.l('Facebook:ErrorSavingDraft') || 'Error saving draft');
                } finally {
                    this.saving = false;
                }
            },
            
            async createCampaign() {
                if (!this.canCreateCampaign) {
                    return;
                }
                
                try {
                    this.saving = true;
                    this.loadingMessage = 'Creating campaign...';
                    
                    // Prepare campaign data for creation
                    const createData = {
                        facebookPostId: this.selectedPost.facebookPostId,
                        facebookPageId: this.selectedPost.facebookPageId,
                        campaignName: this.campaignData.campaignName,
                        description: this.campaignData.description,
                        publicReplyMessage: this.campaignData.publicReplyMessage,
                        privateReplyMessage: this.campaignData.privateReplyMessage,
                        sendPublicReply: this.campaignData.sendPublicReply,
                        sendPrivateReply: this.campaignData.sendPrivateReply,
                        sendLike: this.campaignData.sendLike,
                        endDate: this.campaignData.endDate,
                        maxRepliesPerUser: this.campaignData.maxRepliesPerUser,
                        includePostLinkInPrivateReply: this.campaignData.includePostLinkInPrivateReply,
                        publicReplyType: this.campaignData.publicReplyType,
                        privateReplyType: this.campaignData.privateReplyType
                    };
                    
                    const createdCampaign = await window.shaheerExpress.services.autoReplyCampaign.create(createData);
                    
                    // Clear any saved draft
                    localStorage.removeItem('campaignDraft');
                    
                    abp.notify.success(this.l('Facebook:CampaignCreatedSuccess') || 'Campaign created successfully');
                    
                    // Navigate to campaign details or campaigns list
                    setTimeout(() => {
                        window.location.href = `/Facebook/Campaigns?campaignId=${createdCampaign.id}`;
                    }, 1000);
                    
                } catch (error) {
                    console.error('Error creating campaign:', error);
                    abp.notify.error(this.l('Facebook:ErrorCreatingCampaign') || 'Error creating campaign');
                } finally {
                    this.saving = false;
                }
            },
            
            formatDate(dateString) {
                if (!dateString) return '';
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric', 
                    year: 'numeric' 
                });
            },
            
            loadDraftData() {
                try {
                    const draftData = localStorage.getItem('campaignDraft');
                    if (draftData) {
                        const draft = JSON.parse(draftData);
                        
                        const confirmed = confirm(this.l('Facebook:LoadDraftConfirmation') || 'A draft campaign was found. Would you like to load it?');
                        if (confirmed) {
                            this.selectedPost = draft.selectedPost;
                            this.campaignData = { ...this.campaignData, ...draft.campaignData };
                            
                            // If we have a selected post, we can skip to step 2
                            if (this.selectedPost) {
                                this.currentStep = 2;
                            }
                            
                            this.updateStepValidation();
                            abp.notify.success(this.l('Facebook:DraftLoaded') || 'Draft loaded successfully');
                        }
                    }
                } catch (error) {
                    console.error('Error loading draft:', error);
                    localStorage.removeItem('campaignDraft'); // Clear corrupted draft
                }
            }
        },

        async mounted() {
            // Initialize ABP localization
            this.l = abp.localization.getResource('ShaheerExpress');
            
            // Check permissions
            this.canCreate = abp.auth.isGranted('ShaheerExpress.Campaigns.Create');
            
            if (!this.canCreate) {
                abp.notify.error(this.l('Facebook:NoPermissionToCreateCampaign') || 'You do not have permission to create campaigns');
                this.goBack();
                return;
            }
            
            // Load initial data
            await this.loadInitialData();
            
            // Load draft data if available (only if no template or pre-selected post)
            if (!this.templateId && !this.selectedPostId) {
                this.loadDraftData();
            }
            
            // Initial step validation
            this.updateStepValidation();
        }
    });
    
    // Register components
    app.component('post-selection', PostSelection);
    app.component('campaign-form', CampaignForm);
    
    app.mount('#facebookCampaignCreateApp');
}

// Initialize the Vue app when DOM is ready
document.addEventListener('DOMContentLoaded', initializeVueApp);
