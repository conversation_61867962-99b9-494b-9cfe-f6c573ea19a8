using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ShaheerExpress.Services.Dtos;
using Volo.Abp.Domain.Entities.Auditing;

namespace ShaheerExpress.Entities;

public class ScheduledFacebookPost : FullAuditedAggregateRoot<Guid>
{
    [Required]
    public Guid FacebookPageId { get; set; }

    [StringLength(256)]
    public string FacebookPostId { get; set; } = string.Empty;

    [Required]
    public FacebookPostType PostType { get; set; }

    [StringLength(2048)]
    public string? Message { get; set; }

    [StringLength(4000)] // JSON serialized list of media URLs
    public string? MediaUrlsJson { get; set; }

    [StringLength(1024)]
    public string? LinkUrl { get; set; }

    [Required]
    public DateTime ScheduledPublishTime { get; set; }

    [Required]
    public PostScheduleStatus Status { get; set; }

    [StringLength(1024)]
    public string? ErrorMessage { get; set; }

    public DateTime? PublishedAt { get; set; }

    // Navigation property
    public virtual FacebookPage FacebookPage { get; set; } = null!;

    // Helper properties for media URLs
    public List<string> MediaUrls
    {
        get
        {
            if (string.IsNullOrEmpty(MediaUrlsJson))
                return new List<string>();
            
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<List<string>>(MediaUrlsJson) ?? new List<string>();
            }
            catch
            {
                return new List<string>();
            }
        }
        set
        {
            MediaUrlsJson = value?.Any() == true 
                ? System.Text.Json.JsonSerializer.Serialize(value) 
                : null;
        }
    }

    protected ScheduledFacebookPost()
    {
        // For EF Core
    }

    public ScheduledFacebookPost(
        Guid id,
        Guid facebookPageId,
        FacebookPostType postType,
        string? message,
        List<string>? mediaUrls,
        string? linkUrl,
        DateTime scheduledPublishTime) : base(id)
    {
        FacebookPageId = facebookPageId;
        PostType = postType;
        Message = message;
        MediaUrls = mediaUrls ?? new List<string>();
        LinkUrl = linkUrl;
        ScheduledPublishTime = scheduledPublishTime;
        Status = PostScheduleStatus.Scheduled;
    }

    public void MarkAsPublished(string facebookPostId, DateTime publishedAt)
    {
        FacebookPostId = facebookPostId;
        Status = PostScheduleStatus.Published;
        PublishedAt = publishedAt;
        ErrorMessage = null;
    }

    public void MarkAsFailed(string errorMessage)
    {
        Status = PostScheduleStatus.Failed;
        ErrorMessage = errorMessage;
    }

    public void MarkAsCancelled()
    {
        Status = PostScheduleStatus.Cancelled;
    }

    public void UpdateSchedule(DateTime newScheduledTime)
    {
        ScheduledPublishTime = newScheduledTime;
        if (Status == PostScheduleStatus.Failed)
        {
            Status = PostScheduleStatus.Scheduled;
            ErrorMessage = null;
        }
    }

    public void UpdateContent(string? message, List<string>? mediaUrls, string? linkUrl)
    {
        Message = message;
        MediaUrls = mediaUrls ?? new List<string>();
        LinkUrl = linkUrl;
        
        if (Status == PostScheduleStatus.Failed)
        {
            Status = PostScheduleStatus.Scheduled;
            ErrorMessage = null;
        }
    }

    public bool CanBeModified => Status == PostScheduleStatus.Draft || Status == PostScheduleStatus.Scheduled || Status == PostScheduleStatus.Failed;
    
    public bool CanBePublished => Status == PostScheduleStatus.Scheduled && ScheduledPublishTime <= DateTime.UtcNow;
    
    public bool IsExpired => Status == PostScheduleStatus.Scheduled && ScheduledPublishTime.AddHours(24) < DateTime.UtcNow;
}
