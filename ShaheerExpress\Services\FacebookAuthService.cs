using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ShaheerExpress.Entities;
using ShaheerExpress.Permissions;
using ShaheerExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace ShaheerExpress.Services;

[Authorize]
public class FacebookAuthService : ApplicationService, IFacebookAuthService
{
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly IFacebookPageService _facebookPageService;
    private readonly ILogger<FacebookAuthService> _logger;

    public FacebookAuthService(
        IRepository<FacebookUser, Guid> facebookUserRepository,
        FacebookGraphApiService facebookGraphApiService,
        IFacebookPageService facebookPageService,
        ILogger<FacebookAuthService> logger)
    {
        _facebookUserRepository = facebookUserRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _facebookPageService = facebookPageService;
        _logger = logger;
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> GetCurrentUserFacebookInfoAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);
        
        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected.");
        }

        return ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> CreateOrUpdateFacebookUserAsync(CreateFacebookUserDto input)
    {
        var currentUserId = CurrentUser.GetId();
        _logger.LogInformation("Starting CreateOrUpdateFacebookUserAsync for user {UserId}", currentUserId);

        var existingFacebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (existingFacebookUser != null)
        {
            _logger.LogInformation("Updating existing Facebook user {FacebookUserId} for user {UserId}", existingFacebookUser.Id, currentUserId);

            // Update existing
            existingFacebookUser.UpdateTokens(input.AccessToken, input.RefreshToken, input.TokenExpiresAt);
            existingFacebookUser.UpdateProfile(input.FacebookName, input.ProfilePictureUrl ?? string.Empty);
            existingFacebookUser.Activate();

            await _facebookUserRepository.UpdateAsync(existingFacebookUser);
            _logger.LogInformation("Successfully updated Facebook user {FacebookUserId} in database", existingFacebookUser.Id);

            // Refresh all associated Facebook Pages access tokens after user token update
            _logger.LogInformation("Starting page token refresh for user {UserId}", currentUserId);
            try
            {
                var existingFacebookUserDto = ObjectMapper.Map<FacebookUser,FacebookUserDto>(existingFacebookUser);
                await _facebookPageService.RefreshAllPageTokensForUserAsync(existingFacebookUserDto);
                _logger.LogInformation("Successfully completed page token refresh for user {UserId} after reconnection", currentUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh page tokens for user {UserId} after reconnection. Error: {ErrorMessage}", currentUserId, ex.Message);
                // Don't throw here - user reconnection was successful, page token refresh is secondary
            }

            return ObjectMapper.Map<FacebookUser, FacebookUserDto>(existingFacebookUser);
        }
        else
        {
            _logger.LogInformation("Creating new Facebook user for user {UserId}", currentUserId);

            // Create new
            var facebookUser = new FacebookUser(
                GuidGenerator.Create(),
                input.FacebookId,
                currentUserId,
                input.AccessToken,
                input.FacebookEmail,
                input.FacebookName);

            if (input.RefreshToken != null)
            {
                facebookUser.UpdateTokens(input.AccessToken, input.RefreshToken, input.TokenExpiresAt);
            }

            if (!string.IsNullOrEmpty(input.ProfilePictureUrl))
            {
                facebookUser.UpdateProfile(input.FacebookName, input.ProfilePictureUrl);
            }

            await _facebookUserRepository.InsertAsync(facebookUser);
            _logger.LogInformation("Successfully created new Facebook user {FacebookUserId} for user {UserId}", facebookUser.Id, currentUserId);

            return ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);
        }
    }

    public async Task<string> GetFacebookLoginUrlAsync(string redirectUri)
    {
        return _facebookGraphApiService.GetFacebookLoginUrl(redirectUri);
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> HandleFacebookCallbackAsync(string code, string redirectUri)
    {
        var currentUserId = CurrentUser.GetId();
        _logger.LogInformation("Starting HandleFacebookCallbackAsync for user {UserId} with redirectUri: {RedirectUri}",
            currentUserId, redirectUri);

        try
        {
            // Exchange code for access token
            _logger.LogInformation("Exchanging authorization code for access token for user {UserId}", currentUserId);
            var tokenResponse = await _facebookGraphApiService.ExchangeCodeForTokenAsync(code, redirectUri);
            _logger.LogInformation("Successfully received access token for user {UserId}. Token expires in: {ExpiresIn} seconds",
                currentUserId, tokenResponse.ExpiresIn);

            // Get user info from Facebook
            _logger.LogInformation("Fetching user info from Facebook for user {UserId}", currentUserId);
            var userInfo = await _facebookGraphApiService.GetUserInfoAsync(tokenResponse.AccessToken);
            _logger.LogInformation("Successfully retrieved user info for user {UserId}. Facebook ID: {FacebookId}, Name: {FacebookName}",
                currentUserId, userInfo.Id, userInfo.Name);

            // Create or update Facebook user
            var createDto = new CreateFacebookUserDto
            {
                FacebookId = userInfo.Id,
                UserId = CurrentUser.GetId(),
                AccessToken = tokenResponse.AccessToken,
                FacebookEmail = userInfo.Email ?? string.Empty,
                FacebookName = userInfo.Name,
                ProfilePictureUrl = userInfo.Picture?.Data?.Url,
                TokenExpiresAt = tokenResponse.ExpiresIn.HasValue
                    ? DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn.Value)
                    : null
            };

            _logger.LogInformation("Calling CreateOrUpdateFacebookUserAsync for user {UserId}", currentUserId);
            var result = await CreateOrUpdateFacebookUserAsync(createDto);
            _logger.LogInformation("Successfully completed HandleFacebookCallbackAsync for user {UserId}", currentUserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling Facebook callback for user {UserId}. Exception: {ExceptionType}, Message: {ErrorMessage}",
                currentUserId, ex.GetType().Name, ex.Message);
            throw new UserFriendlyException("Failed to connect Facebook account. Please try again.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task RefreshFacebookTokenAsync(Guid facebookUserId)
    {
        var facebookUser = await _facebookUserRepository.GetAsync(facebookUserId);
        
        if (facebookUser.UserId != CurrentUser.GetId())
        {
            throw new UnauthorizedAccessException();
        }

        // Note: Facebook doesn't provide refresh tokens for user access tokens
        // This method would be used if we implement long-lived tokens
        // For now, we'll just validate the current token
        try
        {
            await _facebookGraphApiService.GetUserInfoAsync(facebookUser.AccessToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Facebook token validation failed for user {UserId}", facebookUser.UserId);
            throw new UserFriendlyException("Facebook token has expired. Please reconnect your account.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Disconnect)]
    public async Task DisconnectFacebookAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);
        
        if (facebookUser != null)
        {
            facebookUser.Deactivate();
            await _facebookUserRepository.UpdateAsync(facebookUser);
        }
    }
    [HttpGet]
    public async Task<bool> IsConnectedToFacebookAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);
        return facebookUser != null;
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task<FacebookConnectionStatusDto> GetConnectionStatusAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        var status = new FacebookConnectionStatusDto
        {
            IsConnected = facebookUser != null && facebookUser.IsActive
        };

        if (facebookUser == null)
        {
            return status;
        }

        status.FacebookUser = ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);

        // Validate the access token
        try
        {
            var validationResult = await _facebookGraphApiService.ValidateAccessTokenAsync(facebookUser.AccessToken);

            status.IsTokenValid = validationResult.IsValid;
            status.IsTokenExpired = validationResult.IsExpired;
            status.TokenExpiresAt = validationResult.ExpiresAt;
            status.MissingScopes = validationResult.MissingScopes;
            status.ErrorMessage = validationResult.ErrorMessage;
            status.RequiresReconnection = !validationResult.IsValid || validationResult.IsExpired || validationResult.MissingScopes.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Facebook token for user {UserId}", currentUserId);
            status.IsTokenValid = false;
            status.RequiresReconnection = true;
            status.ErrorMessage = "Unable to validate Facebook connection. Please reconnect your account.";
        }

        return status;
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> ReconnectFacebookAccountAsync(string redirectUri)
    {
        // This method initiates the reconnection flow by returning the login URL
        // The actual reconnection happens in HandleFacebookCallbackAsync
        var loginUrl = await GetFacebookLoginUrlAsync(redirectUri);

        // For now, we'll throw an exception with the login URL
        // In a real implementation, this might redirect or return the URL differently
        throw new UserFriendlyException($"Please visit this URL to reconnect your Facebook account: {loginUrl}");
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Disconnect)]
    public async Task<bool> RevokePermissionsAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (facebookUser == null)
        {
            return false;
        }

        try
        {
            // Revoke ShaheerExpress on Facebook
            var revoked = await _facebookGraphApiService.RevokeUserPermissionsAsync(facebookUser.AccessToken);

            if (revoked)
            {
                // Deactivate the local Facebook user record
                facebookUser.Deactivate();
                await _facebookUserRepository.UpdateAsync(facebookUser);

                _logger.LogInformation("Successfully revoked Facebook ShaheerExpress for user {UserId}", currentUserId);
                return true;
            }
            else
            {
                _logger.LogWarning("Failed to revoke Facebook ShaheerExpress for user {UserId}", currentUserId);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking Facebook ShaheerExpress for user {UserId}", currentUserId);
            return false;
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task<FacebookTokenValidationDto> ValidateCurrentTokenAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (facebookUser == null)
        {
            return new FacebookTokenValidationDto
            {
                IsValid = false,
                ErrorMessage = "No Facebook account connected"
            };
        }

        try
        {
            var validationResult = await _facebookGraphApiService.ValidateAccessTokenAsync(facebookUser.AccessToken);

            return new FacebookTokenValidationDto
            {
                IsValid = validationResult.IsValid,
                IsExpired = validationResult.IsExpired,
                ExpiresAt = validationResult.ExpiresAt,
                Scopes = validationResult.Scopes,
                MissingScopes = validationResult.MissingScopes,
                ErrorMessage = validationResult.ErrorMessage,
                ErrorCode = validationResult.ErrorCode,
                TokenType = validationResult.TokenType
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Facebook token for user {UserId}", currentUserId);
            return new FacebookTokenValidationDto
            {
                IsValid = false,
                ErrorMessage = "Failed to validate token: " + ex.Message
            };
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task TriggerTokenValidationAsync()
    {
        try
        {
            var tokenValidationJob = LazyServiceProvider.LazyGetRequiredService<FacebookTokenValidationJob>();
            await tokenValidationJob.ValidateTokensAsync();
            _logger.LogInformation("Manual token validation completed for user {UserId}", CurrentUser.GetId());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual token validation for user {UserId}", CurrentUser.GetId());
            throw new UserFriendlyException("Failed to validate tokens. Please try again later.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ViewPages)]
    public async Task<List<FacebookPageTokenStatusDto>> ValidateAllPageTokensAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (facebookUser == null)
        {
            return new List<FacebookPageTokenStatusDto>();
        }

        try
        {
            var facebookPageRepository = LazyServiceProvider.LazyGetRequiredService<IRepository<FacebookPage, Guid>>();
            var userPages = await facebookPageRepository.GetListAsync(p => p.FacebookUserId == facebookUser.Id);

            var results = new List<FacebookPageTokenStatusDto>();

            foreach (var page in userPages)
            {
                var tokenStatus = await ValidatePageTokenInternalAsync(page);
                results.Add(tokenStatus);
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating page tokens for user {UserId}", currentUserId);
            throw new UserFriendlyException("Failed to validate page tokens. Please try again later.");
        }
    }

    [Authorize(ShaheerExpressPermissions.Facebook.ViewPages)]
    public async Task<FacebookPageTokenStatusDto> ValidatePageTokenAsync(Guid pageId)
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (facebookUser == null)
        {
            throw new UserFriendlyException("No Facebook account connected");
        }

        try
        {
            var facebookPageRepository = LazyServiceProvider.LazyGetRequiredService<IRepository<FacebookPage, Guid>>();
            var page = await facebookPageRepository.FirstOrDefaultAsync(p => p.Id == pageId && p.FacebookUserId == facebookUser.Id);

            if (page == null)
            {
                throw new UserFriendlyException("Facebook page not found or access denied");
            }

            return await ValidatePageTokenInternalAsync(page);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating page token for page {PageId}, user {UserId}", pageId, currentUserId);
            throw new UserFriendlyException("Failed to validate page token. Please try again later.");
        }
    }

    /// <summary>
    /// Test method to manually trigger page token refresh for debugging purposes.
    /// This method can be called to test the page token refresh functionality independently.
    /// </summary>
    [Authorize(ShaheerExpressPermissions.Facebook.Connect)]
    public async Task<string> TestPageTokenRefreshAsync()
    {
        var currentUserId = CurrentUser.GetId();
        _logger.LogInformation("Starting TestPageTokenRefreshAsync for user {UserId}", currentUserId);

        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (facebookUser == null)
        {
            var message = "No Facebook account connected";
            _logger.LogWarning(message + " for user {UserId}", currentUserId);
            return message;
        }

        try
        {
            _logger.LogInformation("Calling RefreshAllPageTokensAsync for user {UserId}", currentUserId);
            await RefreshAllPageTokensAsync(facebookUser);

            var successMessage = "Page token refresh completed successfully. Check logs for detailed information.";
            _logger.LogInformation(successMessage + " for user {UserId}", currentUserId);
            return successMessage;
        }
        catch (Exception ex)
        {
            var errorMessage = $"Page token refresh failed: {ex.Message}";
            _logger.LogError(ex, "TestPageTokenRefreshAsync failed for user {UserId}", currentUserId);
            return errorMessage;
        }
    }

    private async Task<FacebookPageTokenStatusDto> ValidatePageTokenInternalAsync(FacebookPage page)
    {
        try
        {
            var validationResult = await _facebookGraphApiService.ValidatePageAccessTokenAsync(page.PageAccessToken);

            return new FacebookPageTokenStatusDto
            {
                PageId = page.Id,
                PageName = page.PageName,
                IsTokenValid = validationResult.IsValid,
                IsTokenExpired = validationResult.IsExpired,
                TokenExpiresAt = validationResult.ExpiresAt,
                MissingScopes = validationResult.MissingScopes,
                ErrorMessage = validationResult.ErrorMessage,
                RequiresReconnection = !validationResult.IsValid || validationResult.IsExpired || validationResult.MissingScopes.Any()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token for page {PageId}", page.Id);
            return new FacebookPageTokenStatusDto
            {
                PageId = page.Id,
                PageName = page.PageName,
                IsTokenValid = false,
                IsTokenExpired = false,
                ErrorMessage = "Failed to validate token: " + ex.Message,
                RequiresReconnection = true
            };
        }
    }

    /// <summary>
    /// Refreshes all Facebook Page access tokens for a user after their main account token is updated.
    /// This is called during the reconnection process to ensure all page tokens are also refreshed.
    /// </summary>
    private async Task RefreshAllPageTokensAsync(FacebookUser facebookUser)
    {
        _logger.LogInformation("Starting RefreshAllPageTokensAsync for user {UserId} (FacebookUser: {FacebookUserId})",
            facebookUser.UserId, facebookUser.Id);

        try
        {
            var facebookPageRepository = LazyServiceProvider.LazyGetRequiredService<IRepository<FacebookPage, Guid>>();

            // Get all pages for this user
            _logger.LogInformation("Fetching existing pages for user {UserId}", facebookUser.UserId);
            var userPages = await facebookPageRepository.GetListAsync(p => p.FacebookUserId == facebookUser.Id);

            _logger.LogInformation("Found {PageCount} existing pages for user {UserId}", userPages.Count, facebookUser.UserId);

            if (!userPages.Any())
            {
                _logger.LogInformation("No Facebook pages found for user {UserId} to refresh tokens", facebookUser.UserId);
                return;
            }

            // Log existing page details
            foreach (var page in userPages)
            {
                _logger.LogInformation("Existing page: {PageId} ({PageName}) - Connected: {IsConnected}, FacebookPageId: {FacebookPageId}",
                    page.Id, page.PageName, page.IsConnected, page.FacebookPageId);
            }

            // Get fresh page information from Facebook using the updated user access token
            _logger.LogInformation("Fetching fresh page information from Facebook API for user {UserId}", facebookUser.UserId);
            var freshPages = await _facebookGraphApiService.GetUserPagesAsync(facebookUser.AccessToken);

            _logger.LogInformation("Retrieved {FreshPageCount} fresh pages from Facebook API for user {UserId}",
                freshPages.Count, facebookUser.UserId);

            // Log fresh page details
            foreach (var freshPage in freshPages)
            {
                _logger.LogInformation("Fresh page from Facebook: {FacebookPageId} ({PageName}) - HasAccessToken: {HasToken}",
                    freshPage.Id, freshPage.Name, !string.IsNullOrEmpty(freshPage.AccessToken));
            }

            var updatedCount = 0;
            var reconnectedCount = 0;

            foreach (var existingPage in userPages)
            {
                _logger.LogInformation("Processing page {PageId} ({PageName}) with FacebookPageId: {FacebookPageId}",
                    existingPage.Id, existingPage.PageName, existingPage.FacebookPageId);

                var freshPageInfo = freshPages.FirstOrDefault(p => p.Id == existingPage.FacebookPageId);

                if (freshPageInfo != null)
                {
                    _logger.LogInformation("Found matching fresh page for {PageId} ({PageName}). Updating access token.",
                        existingPage.Id, existingPage.PageName);

                    // Log token comparison (first 10 characters for security)
                    var oldTokenPrefix = existingPage.PageAccessToken?.Substring(0, Math.Min(10, existingPage.PageAccessToken.Length)) ?? "null";
                    var newTokenPrefix = freshPageInfo.AccessToken?.Substring(0, Math.Min(10, freshPageInfo.AccessToken?.Length ?? 0)) ?? "null";
                    _logger.LogInformation("Token update for page {PageId}: Old token prefix: {OldToken}, New token prefix: {NewToken}",
                        existingPage.Id, oldTokenPrefix, newTokenPrefix);

                    // Update the page access token with the fresh one from Facebook
                    if (!string.IsNullOrEmpty(freshPageInfo.AccessToken))
                    {
                        existingPage.UpdateAccessToken(freshPageInfo.AccessToken);
                        _logger.LogInformation("Updated access token for page {PageId}", existingPage.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Fresh page info for {PageId} ({PageName}) has null or empty access token",
                            existingPage.Id, existingPage.PageName);
                    }

                    // If the page was previously disconnected due to token issues, reconnect it
                    if (!existingPage.IsConnected)
                    {
                        _logger.LogInformation("Page {PageId} ({PageName}) was disconnected, reconnecting after token refresh",
                            existingPage.Id, existingPage.PageName);
                        existingPage.Reconnect();
                        reconnectedCount++;
                    }

                    _logger.LogInformation("Saving updated page {PageId} to database", existingPage.Id);
                    await facebookPageRepository.UpdateAsync(existingPage);
                    updatedCount++;
                    _logger.LogInformation("Successfully updated page {PageId} in database", existingPage.Id);
                }
                else
                {
                    _logger.LogWarning("Facebook page {PageId} ({PageName}) with FacebookPageId {FacebookPageId} not found in fresh page list - user may have lost access",
                        existingPage.Id, existingPage.PageName, existingPage.FacebookPageId);
                }
            }

            _logger.LogInformation("Completed RefreshAllPageTokensAsync for user {UserId}. Updated: {UpdatedCount}, Reconnected: {ReconnectedCount} pages.",
                facebookUser.UserId, updatedCount, reconnectedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RefreshAllPageTokensAsync for user {UserId}. Exception: {ExceptionType}, Message: {ErrorMessage}",
                facebookUser.UserId, ex.GetType().Name, ex.Message);
            throw;
        }
    }
}
