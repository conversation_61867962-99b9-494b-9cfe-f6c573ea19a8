using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ShaheerExpress.Services;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace ShaheerExpress.Pages.Facebook.Campaigns
{
    public class IndexModel : AbpPageModel
    {
        protected IAutoReplyCampaignService _campaignService;
        protected IFacebookPageService _facebookPageService;
        protected IFacebookAuthService _facebookAuthService;

        public IndexModel(
            IAutoReplyCampaignService campaignService,
            IFacebookPageService facebookPageService,
            IFacebookAuthService facebookAuthService)
        {
            _campaignService = campaignService;
            _facebookPageService = facebookPageService;
            _facebookAuthService = facebookAuthService;
        }

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
