using ShaheerExpress.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace ShaheerExpress.Permissions;

public class ShaheerExpressPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(ShaheerExpressPermissions.GroupName);


        myGroup.AddPermission(ShaheerExpressPermissions.Dashboard.Host, L("Permission:Dashboard"), MultiTenancySides.Host);
        myGroup.AddPermission(ShaheerExpressPermissions.Dashboard.Tenant, L("Permission:Dashboard"), MultiTenancySides.Tenant);


        //Define your own ShaheerExpress here. Example:
        //myGroup.AddPermission(ShaheerExpressPermissions.MyPermission1, L("Permission:MyPermission1"));

        var authorPermission = myGroup.AddPermission(ShaheerExpressPermissions.Authors.De<PERSON>ult, L("Permission:Authors"));
        authorPermission.AddChild(ShaheerExpressPermissions.Authors.Create, L("Permission:Create"));
        authorPermission.AddChild(ShaheerExpressPermissions.Authors.Edit, L("Permission:Edit"));
        authorPermission.AddChild(ShaheerExpressPermissions.Authors.Delete, L("Permission:Delete"));

        // Facebook ShaheerExpress
        var facebookPermission = myGroup.AddPermission(ShaheerExpressPermissions.Facebook.Default, L("Permission:Facebook"));
        facebookPermission.AddChild(ShaheerExpressPermissions.Facebook.Connect, L("Permission:Facebook.Connect"));
        facebookPermission.AddChild(ShaheerExpressPermissions.Facebook.Disconnect, L("Permission:Facebook.Disconnect"));
        facebookPermission.AddChild(ShaheerExpressPermissions.Facebook.ViewPages, L("Permission:Facebook.ViewPages"));
        facebookPermission.AddChild(ShaheerExpressPermissions.Facebook.ManagePages, L("Permission:Facebook.ManagePages"));

        // Posts ShaheerExpress
        var postsPermission = myGroup.AddPermission(ShaheerExpressPermissions.Posts.Default, L("Permission:Posts"));
        postsPermission.AddChild(ShaheerExpressPermissions.Posts.View, L("Permission:Posts.View"));
        postsPermission.AddChild(ShaheerExpressPermissions.Posts.Sync, L("Permission:Posts.Sync"));
        postsPermission.AddChild(ShaheerExpressPermissions.Posts.Create, L("Permission:Posts.Create"));
        postsPermission.AddChild(ShaheerExpressPermissions.Posts.Edit, L("Permission:Posts.Edit"));
        postsPermission.AddChild(ShaheerExpressPermissions.Posts.Delete, L("Permission:Posts.Delete"));
        postsPermission.AddChild(ShaheerExpressPermissions.Posts.Publish, L("Permission:Posts.Publish"));
        postsPermission.AddChild(ShaheerExpressPermissions.Posts.Schedule, L("Permission:Posts.Schedule"));

        // Campaigns ShaheerExpress
        var campaignsPermission = myGroup.AddPermission(ShaheerExpressPermissions.Campaigns.Default, L("Permission:Campaigns"));
        campaignsPermission.AddChild(ShaheerExpressPermissions.Campaigns.View, L("Permission:Campaigns.View"));
        campaignsPermission.AddChild(ShaheerExpressPermissions.Campaigns.Create, L("Permission:Campaigns.Create"));
        campaignsPermission.AddChild(ShaheerExpressPermissions.Campaigns.Edit, L("Permission:Campaigns.Edit"));
        campaignsPermission.AddChild(ShaheerExpressPermissions.Campaigns.Delete, L("Permission:Campaigns.Delete"));
        campaignsPermission.AddChild(ShaheerExpressPermissions.Campaigns.Activate, L("Permission:Campaigns.Activate"));
        campaignsPermission.AddChild(ShaheerExpressPermissions.Campaigns.Deactivate, L("Permission:Campaigns.Deactivate"));
        campaignsPermission.AddChild(ShaheerExpressPermissions.Campaigns.ViewActivities, L("Permission:Campaigns.ViewActivities"));

    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<ShaheerExpressResource>(name);
    }
}
