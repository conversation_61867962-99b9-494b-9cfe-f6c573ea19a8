﻿function initializeVueApp() {
    if (typeof Vue === 'undefined') {
        setTimeout(initializeVueApp, 100);
        return;
    }
    else
        createVueApp();
}

function createVueApp() {
    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                authors: [],
                loading: false,
                currentPage: 1,
                pageSize: 10,
                totalItems: 0,
                sortField: 'name',
                sortDirection: 'asc',
                l: null,
                createModal: null,
                editModal: null,
                canEdit: false,
                canDelete: false,
                showAdvancedFilters: false,
                filters: {
                    filterText: '',
                    name: '',
                    birthdateMin: '',
                    birthdateMax: ''
                }
            };
        },
        computed: {
            totalPages() {
                return Math.ceil(this.totalItems / this.pageSize);
            },
            startIndex() {
                return (this.currentPage - 1) * this.pageSize;
            },
            paginatedAuthors() {
                // Since we're doing server-side pagination, return all authors
                return this.authors;
            },
            visiblePages() {
                const pages = [];
                const start = Math.max(1, this.currentPage - 2);
                const end = Math.min(this.totalPages, this.currentPage + 2);

                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            },
            advancedFiltersIcon() {
                return this.showAdvancedFilters ? "fa ms-1 fa-angle-up" : "fa ms-1 fa-angle-down";
            }
        },
        methods: {
            async loadAuthors() {
                this.loading = true;
                try {
                    const params = {
                        skipCount: this.startIndex,
                        maxResultCount: this.pageSize,
                        sorting: `${this.sortField} ${this.sortDirection}`,
                        filterText: this.filters.filterText || undefined,
                        name: this.filters.name || undefined,
                        birthdateMin: this.filters.birthdateMin || undefined,
                        birthdateMax: this.filters.birthdateMax || undefined
                    };

                    const response = await window.shaheerExpress.services.authors.authors.getList(params);
                    this.authors = response.items;
                    this.totalItems = response.totalCount;
                } catch (error) {
                    console.error('Error loading authors:', error);
                    abp.notify.error(this.l('ErrorLoadingAuthors') || 'Error loading authors');
                } finally {
                    this.loading = false;
                }
            },
            sortBy(field) {
                if (this.sortField === field) {
                    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    this.sortField = field;
                    this.sortDirection = 'asc';
                }
                this.currentPage = 1;
                this.loadAuthors();
            },
            getSortIcon(field) {
                if (this.sortField !== field) {
                    return 'fas fa-sort text-muted';
                }
                return this.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            },
            changePage(page) {
                if (page >= 1 && page <= this.totalPages) {
                    this.currentPage = page;
                    this.loadAuthors();
                }
            },
            changePageSize() {
                this.currentPage = 1;
                this.loadAuthors();
            },
            searchAuthors() {
                this.currentPage = 1;
                this.loadAuthors();
            },
            toggleAdvancedFilters() {
                this.showAdvancedFilters = !this.showAdvancedFilters;
            },
            openCreateModal() {
                console.log("openCreateModal");
                this.createModal.open();
            },
            editAuthor(author) {
                this.editModal.open({ id: author.id });
            },
            async deleteAuthor(author) {
                const confirmed = await abp.message.confirm(
                    this.l('DeleteConfirmationMessage'),
                    this.l('AreYouSure')
                );

                if (confirmed) {
                    try {
                        await window.shaheerExpress.services.authors.authors.delete(author.id);
                        abp.notify.info(this.l('SuccessfullyDeleted'));
                        this.loadAuthors();
                    } catch (error) {
                        console.error('Error deleting author:', error);
                        abp.notify.error(this.l('ErrorDeletingAuthor') || 'Error deleting author');
                    }
                }
            },
            formatDate(dateString) {
                if (!dateString) return '';
                return new Date(dateString).toLocaleDateString();
            },
            formatDateTime(dateString) {
                if (!dateString) return '';
                return new Date(dateString).toLocaleString();
            }
        },
        async mounted() {
            // Initialize ABP localization
            this.l = abp.localization.getResource('ShaheerExpress');

            // Initialize modals
            this.createModal = new abp.ModalManager(abp.appPath + 'Authors/CreateModal');
            this.editModal = new abp.ModalManager(abp.appPath + 'Authors/EditModal');

            // Set up modal callbacks
            this.createModal.onResult(() => {
                this.loadAuthors();
            });

            this.editModal.onResult(() => {
                this.loadAuthors();
            });

            // Check ShaheerExpress
            this.canEdit = abp.auth.isGranted('ShaheerExpress.Authors.Edit');
            this.canDelete = abp.auth.isGranted('ShaheerExpress.Authors.Delete');

            // Load initial data
            await this.loadAuthors();
        }
    });

    app.mount('#authorsApp');
}
document.addEventListener('DOMContentLoaded', initializeVueApp);
