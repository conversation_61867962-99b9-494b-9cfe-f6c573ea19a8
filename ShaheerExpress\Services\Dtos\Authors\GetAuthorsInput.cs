using Volo.Abp.Application.Dtos;
using System;

namespace ShaheerExpress.Authors
{
    public class GetAuthorsInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public string? Name { get; set; }
        public DateOnly? BirthdateMin { get; set; }
        public DateOnly? BirthdateMax { get; set; }

        public GetAuthorsInput()
        {

        }
    }
}