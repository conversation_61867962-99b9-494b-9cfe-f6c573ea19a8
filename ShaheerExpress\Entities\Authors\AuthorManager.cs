﻿using JetBrains.Annotations;
using ShaheerExpress.Data.Authors;
using Volo.Abp.Domain.Services;
using Volo.Abp.Guids;
using Volo.Abp;
using Volo.Abp.Data;

namespace ShaheerExpress.Entities.Authors
{
    public class AuthorManager : DomainService
    {
        protected IAuthorRepository _authorRepository;

        public AuthorManager(IAuthorRepository authorRepository)
        {
            _authorRepository = authorRepository;
        }

        public virtual async Task<Author> CreateAsync(
        string name, DateOnly birthdate, string? bio = null)
        {
            Check.NotNullOrWhiteSpace(name, nameof(name));

            var author = new Author(
             GuidGenerator.Create(),
             name, birthdate, bio
             );

            return await _authorRepository.InsertAsync(author);
        }

        public virtual async Task<Author> UpdateAsync(
            Guid id,
            string name, DateOnly birthdate, string? bio = null, [CanBeNull] string? concurrencyStamp = null
        )
        {
            Check.NotNullOrWhiteSpace(name, nameof(name));

            var author = await _authorRepository.GetAsync(id);

            author.Name = name;
            author.Birthdate = birthdate;
            author.Bio = bio;

            author.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _authorRepository.UpdateAsync(author);
        }

    }
}
