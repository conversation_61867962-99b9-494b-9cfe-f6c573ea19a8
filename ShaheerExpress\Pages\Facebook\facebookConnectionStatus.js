const FacebookConnectionStatus = {
    props: {
        showActions: {
            type: Boolean,
            default: true
        }
    },
    
    emits: [
        'connect-clicked',
        'reconnect-clicked', 
        'disconnect-clicked',
        'revoke-permissions-clicked',
        'status-changed'
    ],
    
    data() {
        return {
            connectionStatus: null,
            loading: true,
            l: (key) => key
        };
    },
    
    computed: {
        isConnected() {
            return this.connectionStatus?.isConnected === true;
        },
        
        requiresReconnection() {
            return this.connectionStatus?.requiresReconnection === true;
        },
        
        alertClass() {
            if (!this.isConnected) {
                return 'alert-info';
            }
            return this.requiresReconnection ? 'alert-warning' : 'alert-success';
        },
        
        alertIcon() {
            if (!this.isConnected) {
                return 'fas fa-info-circle';
            }
            return this.requiresReconnection ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
        },
        
        alertTitle() {
            if (!this.isConnected) {
                return this.l('Facebook:NotConnected') || 'Facebook Not Connected';
            }
            return this.requiresReconnection 
                ? this.l('Facebook:ConnectionIssue') || 'Facebook Connection Issue'
                : this.l('Facebook:Connected') || 'Facebook Connected';
        }
    },
    
    template: `
        <div class="facebook-connection-status">
            <div v-if="loading" class="text-center py-3">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">{{ l('Facebook:LoadingStatus') || 'Loading connection status...' }}</p>
            </div>
            
            <div v-else-if="connectionStatus" class="alert" :class="alertClass">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-2">
                            <i :class="alertIcon" class="me-2"></i>
                            {{ alertTitle }}
                        </h5>
                        
                        <!-- Connected Status -->
                        <div v-if="isConnected">
                            <div class="facebook-user-info mb-2">
                                <img v-if="connectionStatus.facebookUser?.profilePictureUrl" 
                                     :src="connectionStatus.facebookUser.profilePictureUrl" 
                                     :alt="connectionStatus.facebookUser.facebookName"
                                     class="facebook-user-avatar me-2">
                                <div>
                                    <strong>{{ connectionStatus.facebookUser?.facebookName }}</strong>
                                    <br>
                                    <small class="text-muted">{{ connectionStatus.facebookUser?.facebookEmail }}</small>
                                </div>
                            </div>
                            
                            <!-- Warning messages for connection issues -->
                            <div v-if="requiresReconnection" class="mt-2">
                                <div v-if="connectionStatus.isTokenExpired" class="small text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ l('Facebook:TokenExpired') || 'Your Facebook access token has expired and needs to be renewed.' }}
                                </div>
                                <div v-else-if="connectionStatus.missingScopes && connectionStatus.missingScopes.length > 0" class="small text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ l('Facebook:MissingPermissions') || 'Missing required permissions:' }} {{ connectionStatus.missingScopes.join(', ') }}
                                </div>
                                <div v-else-if="connectionStatus.errorMessage" class="small text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ connectionStatus.errorMessage }}
                                </div>
                            </div>
                            
                            <!-- Token expiration info -->
                            <div v-if="connectionStatus.tokenExpiresAt && !connectionStatus.isTokenExpired" class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ l('Facebook:TokenExpiresAt') || 'Token expires:' }} {{ formatDateTime(connectionStatus.tokenExpiresAt) }}
                                </small>
                            </div>
                        </div>
                        
                        <!-- Not Connected Status -->
                        <div v-else>
                            <p class="mb-0">{{ l('Facebook:ConnectDescription') || 'Connect your Facebook account to enable auto-reply functionality for your Facebook Pages.' }}</p>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div v-if="showActions" class="ms-3">
                        <div class="d-flex gap-2 flex-wrap">
                            <!-- Connect Button -->
                            <button v-if="!isConnected" 
                                    type="button" 
                                    class="btn btn-primary btn-sm"
                                    @click="$emit('connect-clicked')">
                                <i class="fas fa-link me-1"></i>
                                {{ l('Facebook:Connect') || 'Connect Facebook' }}
                            </button>
                            
                            <!-- Reconnect Button -->
                            <button v-if="isConnected && requiresReconnection" 
                                    type="button" 
                                    class="btn btn-warning btn-sm"
                                    @click="$emit('reconnect-clicked')">
                                <i class="fas fa-redo me-1"></i>
                                {{ l('Facebook:Reconnect') || 'Reconnect' }}
                            </button>
                            
                            <!-- Disconnect Button -->
                            <button v-if="isConnected" 
                                    type="button" 
                                    class="btn btn-outline-danger btn-sm"
                                    @click="$emit('disconnect-clicked')">
                                <i class="fas fa-unlink me-1"></i>
                                {{ l('Facebook:Disconnect') || 'Disconnect' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,
    
    methods: {
        async loadConnectionStatus() {
            try {
                this.loading = true;
                this.connectionStatus = await window.shaheerExpress.services.facebookAuth.getConnectionStatus();
                
                // Emit status change event
                this.$emit('status-changed', this.connectionStatus);
            } catch (error) {
                console.error('Error loading Facebook connection status:', error);
                abp.notify.error(this.l('Facebook:ErrorLoadingStatus') || 'Error loading Facebook connection status');
                
                // Set a default error state
                this.connectionStatus = {
                    isConnected: false,
                    errorMessage: this.l('Facebook:UnableToCheckStatus') || 'Unable to check connection status'
                };
            } finally {
                this.loading = false;
            }
        },
        
        async refreshAsync() {
            await this.loadConnectionStatus();
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleDateString();
        },
        
        formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString();
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },

    async mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
        
        // Load initial connection status
        await this.loadConnectionStatus();
    }
};
