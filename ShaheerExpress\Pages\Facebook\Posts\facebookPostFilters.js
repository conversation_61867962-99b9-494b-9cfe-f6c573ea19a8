const FacebookPostFilters = {
    props: {
        pages: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    
    emits: ['filters-changed'],
    
    data() {
        return {
            filters: {
                facebookPageId: null,
                hasActiveCampaign: null,
                searchText: '',
                fromDate: null,
                toDate: null
            },
            l: null,
            searchTimeout: null
        };
    },
    
    computed: {
        hasPages() {
            return this.pages && this.pages.length > 0;
        }
    },
    
    template: `
        <div class="filters-card">
            <div class="row">
                <!-- Page Filter -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">{{ l('Facebook:Page') || 'Page' }}</label>
                    <select class="form-select" 
                            v-model="filters.facebookPageId" 
                            v-on:change="emitFiltersChanged"
                            :disabled="loading || !hasPages">
                        <option :value="null">{{ l('Facebook:AllPages') || 'All Pages' }}</option>
                        <option v-for="page in pages" 
                                :key="page.id" 
                                :value="page.id">
                            {{ page.pageName }}
                        </option>
                    </select>
                </div>
                
                <!-- Campaign Status Filter -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">{{ l('Facebook:CampaignStatus') || 'Campaign Status' }}</label>
                    <select class="form-select" 
                            v-model="filters.hasActiveCampaign" 
                            v-on:change="emitFiltersChanged">
                        <option :value="null">{{ l('Facebook:AllPosts') || 'All Posts' }}</option>
                        <option :value="true">{{ l('Facebook:WithActiveCampaign') || 'With Active Campaign' }}</option>
                        <option :value="false">{{ l('Facebook:WithoutCampaign') || 'Without Campaign' }}</option>
                    </select>
                </div>
                
                <!-- Search Filter -->
                <div class="col-md-4 mb-3">
                    <label class="form-label">{{ l('Facebook:Search') || 'Search' }}</label>
                    <div class="input-group">
                        <input type="text" 
                               class="form-control" 
                               v-model="filters.searchText"
                               v-on:input="onSearchInput"
                               v-on:keypress="onSearchKeyPress"
                               :placeholder="l('Facebook:SearchPosts') || 'Search posts...'"
                               :disabled="loading">
                        <button class="btn btn-outline-secondary" 
                                type="button" 
                                v-on:click="clearSearch"
                                v-if="filters.searchText">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Search Button -->
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" 
                            class="btn btn-primary d-block w-100" 
                            v-on:click="searchPosts"
                            :disabled="loading">
                        <i class="fas fa-search me-1"></i>
                        {{ l('Facebook:Search') || 'Search' }}
                    </button>
                </div>
            </div>
            
            <!-- Advanced Filters Row -->
            <div class="row">
                <!-- From Date -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">{{ l('Facebook:FromDate') || 'From Date' }}</label>
                    <input type="date" 
                           class="form-control" 
                           v-model="filters.fromDate"
                           v-on:change="emitFiltersChanged"
                           :disabled="loading">
                </div>
                
                <!-- To Date -->
                <div class="col-md-3 mb-3">
                    <label class="form-label">{{ l('Facebook:ToDate') || 'To Date' }}</label>
                    <input type="date" 
                           class="form-control" 
                           v-model="filters.toDate"
                           v-on:change="emitFiltersChanged"
                           :disabled="loading">
                </div>
                
                <!-- Clear Filters -->
                <div class="col-md-6 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="button" 
                                class="btn btn-outline-secondary" 
                                v-on:click="clearAllFilters"
                                :disabled="loading">
                            <i class="fas fa-eraser me-1"></i>
                            {{ l('Facebook:ClearFilters') || 'Clear Filters' }}
                        </button>
                        <button type="button" 
                                class="btn btn-outline-info" 
                                v-on:click="resetToDefaults"
                                :disabled="loading">
                            <i class="fas fa-undo me-1"></i>
                            {{ l('Facebook:ResetFilters') || 'Reset to Defaults' }}
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Active Filters Summary -->
            <div v-if="hasActiveFilters" class="row">
                <div class="col-12">
                    <div class="alert alert-info py-2 mb-0">
                        <small>
                            <strong>{{ l('Facebook:ActiveFilters') || 'Active Filters:' }}</strong>
                            <span v-if="filters.facebookPageId" class="ms-2">
                                <span class="badge bg-primary me-1">{{ getSelectedPageName() }}</span>
                            </span>
                            <span v-if="filters.hasActiveCampaign !== null" class="ms-2">
                                <span class="badge bg-info me-1">
                                    {{ filters.hasActiveCampaign ? (l('Facebook:WithCampaign') || 'With Campaign') : (l('Facebook:WithoutCampaign') || 'Without Campaign') }}
                                </span>
                            </span>
                            <span v-if="filters.searchText" class="ms-2">
                                <span class="badge bg-success me-1">{{ l('Facebook:Search') || 'Search' }}: "{{ filters.searchText }}"</span>
                            </span>
                            <span v-if="filters.fromDate" class="ms-2">
                                <span class="badge bg-warning me-1">{{ l('Facebook:From') || 'From' }}: {{ formatDate(filters.fromDate) }}</span>
                            </span>
                            <span v-if="filters.toDate" class="ms-2">
                                <span class="badge bg-warning me-1">{{ l('Facebook:To') || 'To' }}: {{ formatDate(filters.toDate) }}</span>
                            </span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `,
    
    computed: {
        hasActiveFilters() {
            return this.filters.facebookPageId !== null ||
                   this.filters.hasActiveCampaign !== null ||
                   this.filters.searchText !== '' ||
                   this.filters.fromDate !== null ||
                   this.filters.toDate !== null;
        }
    },
    
    methods: {
        emitFiltersChanged() {
            this.$emit('filters-changed', { ...this.filters });
        },
        
        onSearchInput() {
            // Debounce search input
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            
            this.searchTimeout = setTimeout(() => {
                this.emitFiltersChanged();
            }, 500);
        },
        
        onSearchKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                this.searchPosts();
            }
        },
        
        searchPosts() {
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            this.emitFiltersChanged();
        },
        
        clearSearch() {
            this.filters.searchText = '';
            this.emitFiltersChanged();
        },
        
        clearAllFilters() {
            this.filters = {
                facebookPageId: null,
                hasActiveCampaign: null,
                searchText: '',
                fromDate: null,
                toDate: null
            };
            this.emitFiltersChanged();
        },
        
        resetToDefaults() {
            this.filters = {
                facebookPageId: null,
                hasActiveCampaign: false, // Default to posts without campaigns
                searchText: '',
                fromDate: null,
                toDate: null
            };
            this.emitFiltersChanged();
        },
        
        getSelectedPageName() {
            if (!this.filters.facebookPageId || !this.hasPages) return '';
            
            const selectedPage = this.pages.find(page => page.id === this.filters.facebookPageId);
            return selectedPage ? selectedPage.pageName : '';
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString();
        },
        
        // Public method to set filters from parent component
        setFilters(newFilters) {
            this.filters = { ...this.filters, ...newFilters };
        }
    },

    async created() {
        // Load localization earlier in lifecycle
        try {
            if (typeof abp !== 'undefined' && abp.localization) {
                this.l = abp.localization.getResource('ShaheerExpress');
            }
        } catch (e) {
            console.error('Localization init error:', e);
            // Keep fallback function
        }
    },

    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    },
    
    beforeUnmount() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
};
