using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace ShaheerExpress.Services.Dtos;

public class PublishFacebookPostDto
{
    [Required]
    public Guid FacebookPageId { get; set; }

    [Required]
    public FacebookPostType PostType { get; set; }

    [StringLength(2048)]
    public string? Message { get; set; }

    public List<FacebookPostMediaDto> Media { get; set; } = new();

    [StringLength(1024)]
    public string? LinkUrl { get; set; }

    public bool PublishNow { get; set; } = true;

    public DateTime? ScheduledPublishTime { get; set; }

    // Validation: Either message or media must be provided
    public bool IsValid => !string.IsNullOrWhiteSpace(Message) || Media.Any() || !string.IsNullOrWhiteSpace(LinkUrl);
}

public class FacebookPostMediaDto
{
    [Required]
    [StringLength(256)]
    public string FileName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string ContentType { get; set; } = string.Empty;

    [Required]
    public byte[] Content { get; set; } = Array.Empty<byte>();

    public long Size { get; set; }

    public bool IsImage => ContentType.StartsWith("image/");
    public bool IsVideo => ContentType.StartsWith("video/");
}

public class FacebookPostPublishResultDto
{
    public bool Success { get; set; }
    public string? FacebookPostId { get; set; }
    public string? ErrorMessage { get; set; }
    public FacebookErrorType? ErrorType { get; set; }
    public PostScheduleStatus Status { get; set; }
    public DateTime? PublishedAt { get; set; }
    public DateTime? ScheduledFor { get; set; }
}

public class FacebookPostPreviewDto
{
    public string? Message { get; set; }
    public List<string> MediaUrls { get; set; } = new();
    public string? LinkUrl { get; set; }
    public string? LinkTitle { get; set; }
    public string? LinkDescription { get; set; }
    public string? LinkImageUrl { get; set; }
    public FacebookPostType PostType { get; set; }
    public string PageName { get; set; } = string.Empty;
    public string PageProfilePictureUrl { get; set; } = string.Empty;
}

public class FacebookLinkPreviewDto
{
    public string Url { get; set; } = string.Empty;
    public string? Title { get; set; }
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public string? SiteName { get; set; }
    public bool IsValid { get; set; }
}

public class ScheduledFacebookPostDto : FullAuditedEntityDto<Guid>
{
    public Guid FacebookPageId { get; set; }
    public string FacebookPostId { get; set; } = string.Empty;
    public FacebookPostType PostType { get; set; }
    public string? Message { get; set; }
    public List<string> MediaUrls { get; set; } = new();
    public string? LinkUrl { get; set; }
    public DateTime ScheduledPublishTime { get; set; }
    public PostScheduleStatus Status { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime? PublishedAt { get; set; }
    
    // Page information for display
    public string PageName { get; set; } = string.Empty;
    public string PageProfilePictureUrl { get; set; } = string.Empty;
}

public class GetScheduledPostsInput : PagedAndSortedResultRequestDto
{
    public Guid? FacebookPageId { get; set; }
    public PostScheduleStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class UpdateScheduledPostDto
{
    [StringLength(2048)]
    public string? Message { get; set; }
    
    public DateTime? ScheduledPublishTime { get; set; }
    
    public PostScheduleStatus? Status { get; set; }
}

// Request models for Facebook Graph API
public class FacebookPostPublishRequest
{
    public string? Message { get; set; }
    public List<string> MediaIds { get; set; } = new();
    public string? Link { get; set; }
    public bool Published { get; set; } = true;
    public long? ScheduledPublishTime { get; set; } // Unix timestamp
}

public class FacebookMediaUploadRequest
{
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
}

public class FacebookMediaUploadResponse
{
    public string Id { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}
