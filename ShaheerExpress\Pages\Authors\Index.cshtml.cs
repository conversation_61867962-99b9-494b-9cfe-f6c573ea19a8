using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ShaheerExpress.Services.Authors;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace ShaheerExpress.Pages.Authors
{
    public class IndexModel : AbpPageModel
    {
        public string? NameFilter { get; set; }
        public DateOnly? BirthdateFilterMin { get; set; }

        public DateOnly? BirthdateFilterMax { get; set; }

        protected IAuthorsAppService _authorsAppService;

        public IndexModel(IAuthorsAppService authorsAppService)
        {
            _authorsAppService = authorsAppService;
        }

        public virtual async Task OnGetAsync()
        {

            await Task.CompletedTask;
        }
    }
}
