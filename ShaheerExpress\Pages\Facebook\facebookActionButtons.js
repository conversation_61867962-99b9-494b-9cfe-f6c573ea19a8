const FacebookActionButtons = {
    props: {
        showConnectButton: {
            type: Boolean,
            default: false
        },
        showReconnectButton: {
            type: Boolean,
            default: false
        },
        showValidateButton: {
            type: Boolean,
            default: false
        },
        showDisconnectButton: {
            type: <PERSON>olean,
            default: false
        },
        showRevokeButton: {
            type: <PERSON>olean,
            default: false
        }
    },
    
    emits: [
        'action-completed',
        'error'
    ],
    
    data() {
        return {
            isProcessing: false,
            processingMessage: '',
            l: null
        };
    },
    
    template: `
        <div class="facebook-action-buttons">
            <div v-if="isProcessing" class="d-flex align-items-center">
                <div class="loading-spinner me-2"></div>
                <span>{{ processingMessage }}</span>
            </div>
            
            <div v-else class="d-flex gap-2 flex-wrap">
                <!-- Connect Button -->
                <button v-if="showConnectButton" 
                        type="button" 
                        class="btn btn-primary"
                        @click="connectFacebook"
                        :disabled="isProcessing">
                    <i class="fas fa-link me-1"></i>
                    {{ l('Facebook:Connect') || 'Connect Facebook' }}
                </button>
                
                <!-- Reconnect Button -->
                <button v-if="showReconnectButton" 
                        type="button" 
                        class="btn btn-warning"
                        @click="reconnectFacebook"
                        :disabled="isProcessing">
                    <i class="fas fa-redo me-1"></i>
                    {{ l('Facebook:Reconnect') || 'Reconnect Facebook' }}
                </button>
                
                <!-- Validate Token Button -->
                <button v-if="showValidateButton" 
                        type="button" 
                        class="btn btn-outline-info"
                        @click="validateToken"
                        :disabled="isProcessing">
                    <i class="fas fa-shield-alt me-1"></i>
                    {{ l('Facebook:ValidateToken') || 'Validate Token' }}
                </button>
                
                <!-- Disconnect Button -->
                <button v-if="showDisconnectButton" 
                        type="button" 
                        class="btn btn-outline-danger"
                        @click="disconnectFacebook"
                        :disabled="isProcessing">
                    <i class="fas fa-unlink me-1"></i>
                    {{ l('Facebook:Disconnect') || 'Disconnect' }}
                </button>
                
                <!-- Revoke Permissions Button -->
                <button v-if="showRevokeButton" 
                        type="button" 
                        class="btn btn-outline-warning"
                        @click="revokePermissions"
                        :disabled="isProcessing">
                    <i class="fas fa-user-times me-1"></i>
                    {{ l('Facebook:RevokePermissions') || 'Revoke Permissions' }}
                </button>
            </div>
        </div>
    `,
    
    methods: {
        async connectFacebook() {
            try {
                this.isProcessing = true;
                this.processingMessage = this.l('Facebook:RedirectingToFacebook') || 'Redirecting to Facebook...';
                
                const redirectUri = this.getRedirectUri();
                const loginUrl = await window.shaheerExpress.services.facebookAuth.getFacebookLoginUrl(redirectUri);
                
                // Redirect to Facebook
                window.location.href = loginUrl;
            } catch (error) {
                await this.handleError(this.l('Facebook:ErrorConnecting') || 'Failed to initiate Facebook connection', error);
            } finally {
                this.isProcessing = false;
            }
        },
        
        async reconnectFacebook() {
            try {
                this.isProcessing = true;
                this.processingMessage = this.l('Facebook:ReconnectingToFacebook') || 'Reconnecting to Facebook...';
                
                const redirectUri = this.getRedirectUri();
                const loginUrl = await window.shaheerExpress.services.facebookAuth.getFacebookLoginUrl(redirectUri);
                
                // Redirect to Facebook
                window.location.href = loginUrl;
            } catch (error) {
                await this.handleError(this.l('Facebook:ErrorReconnecting') || 'Failed to reconnect Facebook account', error);
            } finally {
                this.isProcessing = false;
            }
        },
        
        async validateToken() {
            try {
                this.isProcessing = true;
                this.processingMessage = this.l('Facebook:ValidatingToken') || 'Validating Facebook token...';
                
                const validation = await window.shaheerExpress.services.facebookAuth.validateCurrentToken();
                
                if (validation.isValid) {
                    abp.notify.success(this.l('Facebook:TokenValidationSuccess') || 'Facebook token is valid');
                } else {
                    const message = validation.errorMessage || 
                                  this.l('Facebook:TokenValidationFailed') || 
                                  'Facebook token validation failed';
                    abp.notify.warn(message);
                }
                
                this.$emit('action-completed');
            } catch (error) {
                await this.handleError(this.l('Facebook:ErrorValidatingToken') || 'Failed to validate Facebook token', error);
            } finally {
                this.isProcessing = false;
            }
        },
        
        async disconnectFacebook() {
            const confirmed = await abp.message.confirm(
                this.l('Facebook:DisconnectConfirmationMessage') || 'Are you sure you want to disconnect your Facebook account?',
                this.l('AreYouSure') || 'Are you sure?'
            );
            
            if (confirmed) {
                try {
                    this.isProcessing = true;
                    this.processingMessage = this.l('Facebook:DisconnectingAccount') || 'Disconnecting Facebook account...';
                    
                    await window.shaheerExpress.services.facebookAuth.disconnectFacebook();
                    abp.notify.info(this.l('Facebook:DisconnectedSuccessfully') || 'Facebook account disconnected successfully');
                    
                    this.$emit('action-completed');
                } catch (error) {
                    await this.handleError(this.l('Facebook:ErrorDisconnecting') || 'Failed to disconnect Facebook account', error);
                } finally {
                    this.isProcessing = false;
                }
            }
        },
        
        async revokePermissions() {
            const confirmed = await abp.message.confirm(
                this.l('Facebook:RevokePermissionsConfirmationMessage') || 'Are you sure you want to revoke all Facebook permissions? This will disconnect your account.',
                this.l('AreYouSure') || 'Are you sure?'
            );
            
            if (confirmed) {
                try {
                    this.isProcessing = true;
                    this.processingMessage = this.l('Facebook:RevokingPermissions') || 'Revoking Facebook permissions...';
                    
                    const success = await window.shaheerExpress.services.facebookAuth.revokePermissions();
                    
                    if (success) {
                        abp.notify.info(this.l('Facebook:PermissionsRevokedSuccessfully') || 'Facebook permissions revoked successfully');
                    } else {
                        abp.notify.warn(this.l('Facebook:PermissionsRevokePartial') || 'Some permissions may not have been revoked');
                    }
                    
                    this.$emit('action-completed');
                } catch (error) {
                    await this.handleError(this.l('Facebook:ErrorRevokingPermissions') || 'Failed to revoke Facebook permissions', error);
                } finally {
                    this.isProcessing = false;
                }
            }
        },
        
        getRedirectUri() {
            const baseUrl = window.location.origin;
            return `${baseUrl}/Facebook`;
        },
        
        async handleError(message, error) {
            console.error(message, error);
            this.$emit('error', `${message}: ${error.message || error}`);
        }
    },
    
    mounted() {
        // Initialize ABP localization
        this.l = abp.localization.getResource('ShaheerExpress');
    }
};
