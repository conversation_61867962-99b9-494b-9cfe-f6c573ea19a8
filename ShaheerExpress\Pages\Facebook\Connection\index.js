function initializeVueApp() {
    if (typeof Vue === 'undefined') {
        setTimeout(initializeVueApp, 100);
        return;
    }
    else
        createVueApp();
}

function createVueApp() {
    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                connectionStatus: null,
                tokenValidation: null,
                loading: false,
                loadingMessage: 'Loading...',
                showTokenDetails: false,
                l: null,
                
                // Redirection handling
                redirectSource: null,
                returnUrl: null,
                showRedirectionAlert: false,
                
                // Permissions
                canConnect: false,
                canDisconnect: false
            };
        },
        
        computed: {
            isConnected() {
                return this.connectionStatus?.isConnected === true;
            },
            
            requiresReconnection() {
                return this.connectionStatus?.requiresReconnection === true;
            }
        },
        
        methods: {
            async loadInitialData() {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Loading Facebook connection status...';
                    
                    // The FacebookConnectionStatus component will load its own data
                    // We just need to wait a moment for it to initialize
                    await new Promise(resolve => setTimeout(resolve, 100));
                } finally {
                    this.loading = false;
                }
            },
            
            async refreshStatus() {
                if (this.$refs.connectionStatusComponent) {
                    await this.$refs.connectionStatusComponent.refreshAsync();
                }
            },
            
            async handleConnectClicked() {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Redirecting to Facebook...';
                    
                    const redirectUri = this.getRedirectUri();
                    const loginUrl = await window.shaheerExpress.services.facebookAuth.getFacebookLoginUrl(redirectUri);
                    
                    // Redirect to Facebook
                    window.location.href = loginUrl;
                } catch (error) {
                    console.error('Error connecting to Facebook:', error);
                    abp.notify.error(this.l('Facebook:ErrorConnecting') || 'Failed to connect to Facebook');
                } finally {
                    this.loading = false;
                }
            },
            
            async handleReconnectClicked() {
                try {
                    this.loading = true;
                    this.loadingMessage = 'Reconnecting to Facebook...';
                    
                    const redirectUri = this.getRedirectUri();
                    const loginUrl = await window.shaheerExpress.services.facebookAuth.getFacebookLoginUrl(redirectUri);
                    
                    // Redirect to Facebook
                    window.location.href = loginUrl;
                } catch (error) {
                    console.error('Error reconnecting to Facebook:', error);
                    abp.notify.error(this.l('Facebook:ErrorReconnecting') || 'Failed to reconnect to Facebook');
                } finally {
                    this.loading = false;
                }
            },
            
            async handleDisconnectClicked() {
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:DisconnectConfirmationMessage') || 'Are you sure you want to disconnect your Facebook account?',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Disconnecting Facebook account...';
                        
                        await window.shaheerExpress.services.facebookAuth.disconnectFacebook();
                        abp.notify.info(this.l('Facebook:DisconnectedSuccessfully') || 'Facebook account disconnected successfully');
                        
                        // Refresh the connection status
                        await this.refreshStatus();
                    } catch (error) {
                        console.error('Error disconnecting Facebook:', error);
                        abp.notify.error(this.l('Facebook:ErrorDisconnecting') || 'Failed to disconnect Facebook account');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            async handleRevokePermissionsClicked() {
                const confirmed = await abp.message.confirm(
                    this.l('Facebook:RevokePermissionsConfirmationMessage') || 'Are you sure you want to revoke all Facebook permissions? This will disconnect your account.',
                    this.l('AreYouSure') || 'Are you sure?'
                );
                
                if (confirmed) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Revoking Facebook permissions...';
                        
                        const success = await window.shaheerExpress.services.facebookAuth.revokePermissions();
                        if (success) {
                            abp.notify.info(this.l('Facebook:PermissionsRevokedSuccessfully') || 'Facebook permissions revoked successfully');
                        } else {
                            abp.notify.warn(this.l('Facebook:PermissionsRevokePartial') || 'Some permissions may not have been revoked');
                        }
                        
                        // Refresh the connection status
                        await this.refreshStatus();
                    } catch (error) {
                        console.error('Error revoking Facebook permissions:', error);
                        abp.notify.error(this.l('Facebook:ErrorRevokingPermissions') || 'Failed to revoke Facebook permissions');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            handleStatusChanged(status) {
                this.connectionStatus = status;
            },
            
            async handleActionCompleted() {
                // Refresh the connection status after any action
                await this.refreshStatus();
            },
            
            handleActionError(errorMessage) {
                abp.notify.error(errorMessage);
            },
            
            getRedirectUri() {
                const baseUrl = window.location.origin;
                let redirectUri = `${baseUrl}/Facebook/Connection`;
                
                // Add return URL if specified
                if (this.returnUrl) {
                    redirectUri += `?returnUrl=${encodeURIComponent(this.returnUrl)}`;
                }
                
                return redirectUri;
            },
            
            async handleFacebookCallback(query) {
                if (query.code && query.code.length > 0) {
                    try {
                        this.loading = true;
                        this.loadingMessage = 'Processing Facebook connection...';
                        
                        const redirectUri = this.getRedirectUri();
                        const result = await window.shaheerExpress.services.facebookAuth.handleFacebookCallback(
                            query.code[0], 
                            redirectUri
                        );
                        
                        abp.notify.success(this.l('Facebook:ConnectedSuccessfully') || 'Facebook account connected successfully');
                        
                        // Clean up URL
                        const cleanUrl = window.location.pathname;
                        window.history.replaceState({}, document.title, cleanUrl);
                        
                        // Refresh the connection status
                        await this.refreshStatus();
                        
                        // Redirect to return URL if specified
                        if (this.returnUrl) {
                            setTimeout(() => {
                                window.location.href = this.returnUrl;
                            }, 1500);
                        }
                    } catch (error) {
                        console.error('Error handling Facebook callback:', error);
                        abp.notify.error(this.l('Facebook:ErrorConnecting') || 'Failed to connect Facebook account');
                    } finally {
                        this.loading = false;
                    }
                }
            },
            
            checkForRedirectionParameters() {
                const urlParams = new URLSearchParams(window.location.search);
                
                if (urlParams.has('from')) {
                    const fromParam = urlParams.get('from');
                    this.redirectSource = {
                        'pages': 'Facebook Pages',
                        'posts': 'Facebook Posts',
                        'campaigns': 'Auto-Reply Campaigns'
                    }[fromParam] || 'another page';
                    this.showRedirectionAlert = true;
                }
                
                if (urlParams.has('returnUrl')) {
                    this.returnUrl = urlParams.get('returnUrl');
                }
            },
            
            formatDate(dateString) {
                if (!dateString) return '';
                return new Date(dateString).toLocaleDateString();
            },
            
            formatDateTime(dateString) {
                if (!dateString) return '';
                return new Date(dateString).toLocaleString();
            }
        },

        async created() {
            // Load localization earlier in lifecycle
            try {
                if (typeof abp !== 'undefined' && abp.localization) {
                    this.l = abp.localization.getResource('ShaheerExpress');
                }
            } catch (e) {
                console.error('Localization init error:', e);
                // Keep fallback function
            }
        },

        async mounted() {
            // Initialize ABP localization
            this.l = abp.localization.getResource('ShaheerExpress');
            
            // Check permissions
            this.canConnect = abp.auth.isGranted('ShaheerExpress.Facebook.Connect');
            this.canDisconnect = abp.auth.isGranted('ShaheerExpress.Facebook.Disconnect');
            
            // Check for redirection parameters
            this.checkForRedirectionParameters();
            
            // Load initial data
            await this.loadInitialData();
            
            // Check for Facebook callback after render
            this.$nextTick(async () => {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('code')) {
                    const query = {};
                    urlParams.forEach((value, key) => {
                        query[key] = [value];
                    });
                    await this.handleFacebookCallback(query);
                }
            });
        }
    });
    
    // Register components (will be defined in separate files)
    app.component('facebook-connection-status', FacebookConnectionStatus);
    app.component('facebook-action-buttons', FacebookActionButtons);
    
    app.mount('#facebookApp');
}

// Initialize the Vue app when DOM is ready
document.addEventListener('DOMContentLoaded', initializeVueApp);
