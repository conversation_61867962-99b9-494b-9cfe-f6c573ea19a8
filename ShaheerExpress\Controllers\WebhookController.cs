﻿using Microsoft.AspNetCore.Mvc;
using ShaheerExpress.Services;
using System.Security.Cryptography;
using System.Text;
using Volo.Abp.AspNetCore.Mvc;

namespace ShaheerExpress.Controllers
{
    [Route("api/webhook")]
    [ApiController]
    public class WebhookController : AbpControllerBase
    {
        private readonly IWebhookService _webhookService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<WebhookController> _logger;

        public WebhookController(
            IWebhookService webhookService,
            IConfiguration configuration,
            ILogger<WebhookController> logger)
        {
            _webhookService = webhookService;
            _configuration = configuration;
            _logger = logger;
        }

        [HttpGet("facebook")]
        public async Task<IActionResult> VerifyFacebookWebhook(
            [FromQuery(Name = "hub.mode")] string mode,
            [FromQuery(Name = "hub.verify_token")] string token,
            [FromQuery(Name = "hub.challenge")] string challenge)
        {
            try
            {
                var result = await _webhookService.VerifyWebhookAsync(mode, token, challenge);
                if (!string.IsNullOrEmpty(result))
                {
                    return Ok(result);
                }
                return Forbid();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying Facebook webhook");
                return Forbid();
            }
        }

        [HttpPost("facebook")]
        [IgnoreAntiforgeryToken]
        public async Task<IActionResult> HandleFacebookWebhook()
        {
            try
            {
                // Make sure the body can be read multiple times if needed:
                Request.EnableBuffering();
                Request.Body.Position = 0;

                using var reader = new StreamReader(
                    Request.Body,
                    encoding: Encoding.UTF8,
                    detectEncodingFromByteOrderMarks: false,
                    bufferSize: 1024,
                    leaveOpen: true      // <-- don’t close Request.Body when disposing
                );

                var payload = await reader.ReadToEndAsync();

                //var signature = Request.Headers["X-Hub-Signature-256"].ToString();

                //if (!ValidateSignature(payload, signature))
                //{
                //    _logger.LogWarning("Invalid webhook signature");
                //    return Forbid();
                //}

                await _webhookService.ProcessWebhookAsync(payload);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling Facebook webhook");
                return StatusCode(500);
            }
        }

        private bool ValidateSignature(string payload, string signature)
        {
            if (string.IsNullOrEmpty(signature) || !signature.StartsWith("sha256="))
            {
                return false;
            }

            var appSecret = _configuration["Facebook:AppSecret"];
            if (string.IsNullOrEmpty(appSecret))
            {
                _logger.LogError("Facebook App Secret not configured");
                return false;
            }

            var expectedSignature = "sha256=" + ComputeHmacSha256(payload, appSecret);
            return signature.Equals(expectedSignature, StringComparison.OrdinalIgnoreCase);
        }

        private static string ComputeHmacSha256(string data, string key)
        {
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToHexString(hash).ToLower();
        }
    }
}
